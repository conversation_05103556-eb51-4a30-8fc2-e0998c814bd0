P -935.0481, 75.2799, -83.9978, 255, 0, 0, 3, to_The_Scarlet_Desert
P -3514.1193, 17.0260, -7.9979, 255, 0, 0, 3, to_<PERSON>shroud_Peaks
P -2409.0000, -198.0000, 0.0000, 255, 255, 255, 2, 2nd_floor
P -3461.0000, 19.0000, -8.6214, 255, 0, 0, 2, Succor
P -3229.8139, 167.3390, -34.3864, 240, 240, 240, 2, Tomb
P -3086.4252, 118.2781, -51.9979, 240, 240, 240, 2, Tomb
P -1236.4671, 277.5039, -83.9979, 240, 240, 240, 2, Tombs
P -1122.8714, 213.7850, -87.9060, 240, 240, 240, 2, Bedroom
P -1336.6591, -189.4153, -83.9979, 240, 240, 240, 2, Treasure_Chests
P -2320.5944, -194.8405, -57.9979, 240, 240, 0, 2, Locked_<PERSON>_(<PERSON>rieg`s_Key,_Not_Pickable)
P -2389.0419, -167.9042, 111.0018, 240, 240, 0, 2, Teleporter_to_First_Floor
P -2387.9782, -193.3184, -57.9979, 240, 240, 0, 2, Teleporter_to_Second_Floor
P -2323.7309, 584.0034, -57.9979, 240, 240, 0, 2, <PERSON>d_Door_(Grieg`s_<PERSON>,Not_Pickable)
P -3068.8886, 288.2787, -49.9979, 240, 240, 240, 2, Mush<PERSON>_Laboratory
P -3180.8437, 343.7631, -49.9979, 240, 240, 240, 2, Fence
P -2896.4729, -54.1216, -57.9979, 240, 240, 240, 2, Dandelions
P -3002.5490, -33.6468, -49.9979, 240, 240, 240, 2, Oven
P -3100.1564, -179.8628, -45.9979, 240, 240, 240, 2, Spire
P -2820.8725, -125.5714, -57.9979, 240, 240, 240, 2, Press
P -2831.1801, 546.2105, -57.9979, 240, 240, 240, 2, Bedroom
P -2873.3405, 499.6205, -57.9979, 240, 240, 240, 2, Armory
P -2863.9594, 133.8713, -57.9979, 240, 240, 240, 2, Haystacks
P -2502.5651, -166.3636, -57.9979, 240, 240, 240, 2, Multi-Spires
P -2507.0717, 450.7761, -57.8657, 240, 240, 240, 2, Bedroom
P -2513.7395, 582.2825, -57.8904, 240, 240, 240, 2, Waiting_Area
P -2422.5905, 454.6576, -57.9979, 240, 240, 240, 2, Crates
P -2424.0407, 301.0917, -57.9979, 240, 240, 240, 2, Bar
P -2328.3903, 202.1134, -57.9979, 240, 240, 240, 2, Pillar_Room
P -2219.3173, 87.0454, -57.9979, 240, 240, 240, 2, Barracks
P -2132.6875, 165.4237, -57.9979, 240, 240, 240, 2, Tombs
P -1721.6461, 252.5730, -83.9978, 240, 240, 240, 2, Tomb
P -1500.2696, 245.2047, -66.9979, 240, 240, 240, 2, Fence
P -1415.7980, 455.0412, -66.3284, 240, 240, 240, 2, Spire_Altar
P -1339.3046, 208.6997, -83.9978, 0, 0, 0, 2, Confused_Pugilist
P -1797.1856, 640.5741, -74.9969, 0, 0, 0, 2, an_ancient_necromantic_shade
P -3151.3676, -266.6775, -45.9979, 0, 0, 0, 2, Stonemason_Galt
P -3353.3493, -79.2856, -27.9979, 0, 0, 0, 2, Alchemist_Varona
P -2347.2099, -488.0295, 158.0018, 240, 127, 0, 2, Grieg_Veneficus_(Raid)
P -1455.4002, -309.9131, -66.9979, 240, 127, 0, 2, Praetorian_Myral_(Raid)
P -2375.8815, 589.4378, -57.9979, 240, 127, 0, 2, Servitor_of_Luclin_(Raid)
P -2155.9284, 328.3909, -57.8757, 127, 64, 0, 2, Amnarra_Nogzabin_(Hunter)
P -2416.6333, -114.9195, -57.9979, 127, 64, 0, 2, Amnarra_Nogzabin_(Hunter)
P -3471.2121, -34.4416, -9.9979, 127, 64, 0, 2, Bronus_(Hunter)
P -2467.1564, -152.0152, -57.9979, 127, 64, 0, 2, Hyraja_Mazarduruk_(Hunter)
P -2659.5563, 352.0082, -57.9979, 127, 64, 0, 2, Kallian_Agamuzum_(Hunter)
P -2108.8300, 578.5804, -57.9979, 127, 64, 0, 2, Khemot_Agarthizar_(Hunter)
P -2680.0285, 167.1134, -57.9979, 127, 64, 0, 2, Doreo_Bexuarrana_(Hunter)
P -3255.7670, 88.6523, -27.9979, 127, 64, 0, 2, Bronus_(Hunter)
P -3390.3012, -205.6070, -15.9979, 127, 64, 0, 2, Deoreo_Bexuarrana_(Hunter)
P -3198.3261, 361.4998, -49.9979, 127, 64, 0, 2, Kalian_Agamuzum_(Hunter)
P -2413.7326, 228.6045, -57.9979, 127, 64, 0, 2, Hyraja_Maarduruk_(Hunter)
