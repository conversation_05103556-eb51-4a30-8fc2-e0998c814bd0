//------------------------------------------------------------------------------
// File: MedParam.idl
//
// Desc: Definition of the IMediaParams and associated interfaces. These
//       interfaces are designed to allow communication of curve-following
//       behaviors for parameters of objects which require dynamic changes
//       to their parameters at run time. All changes are specified by
//       timestamp and curve type to ensure the parameters can be set
//       at sufficient accuracy with predictable behavior on subsequent
//       playback of the same curves.
//
// Copyright (c) 1999 - 2000, Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------------------------


import "oaidl.idl";
import "ocidl.idl";
import "strmif.idl";


//------------------------------------------------------------------------------
// Define the semantic type to be used for each parameter. All values passed
// into this interface are 32-bit floats, but the interface can specify that
// the values must be integer, or booleans or enumerated types
//------------------------------------------------------------------------------
typedef float MP_DATA;          // All data is 32-bit floats

typedef enum _MP_Type {
   MPT_INT,                     // data is signed 23 bit integer (mantissa)
   MPT_FLOAT,                   // data is 32bit IEEE float
   MPT_BOOL,                    // data is true or false (using ANSI C++ definition)
   MPT_ENUM,                    // data is a set (represented by consecutive integers)
   MPT_MAX,
} MP_TYPE;

const MP_DATA MPBOOL_TRUE = 1.0;    // Value of true
const MP_DATA MPBOOL_FALSE = 0.0;   // Value of false


//------------------------------------------------------------------------------
// Define the types of curves which are supported
//------------------------------------------------------------------------------
typedef enum _MP_CURVE_TYPE {
    MP_CURVE_JUMP      = 0x0001, // No interpolation, just jump to next point
    MP_CURVE_LINEAR    = 0x0002, // Linear interpolation (y follows x from 0.0 to 1.0)
    MP_CURVE_SQUARE    = 0x0004, // y follow x^2 from 0.0 to 1.0
    MP_CURVE_INVSQUARE = 0x0008, // y follows 1-(x^2) from 0.0 to 1.0
    MP_CURVE_SINE      = 0x0010, // y follows sin(x) from -pi/2 to pi/2
} MP_CURVE_TYPE;


//------------------------------------------------------------------------------
// Capability bits. Used by the object to specify what capabilities it has.
//------------------------------------------------------------------------------
typedef DWORD MP_CAPS;
// Curve capabilities - If the cap bit is set, that type of curve is supported
const MP_CAPS MP_CAPS_CURVE_JUMP = MP_CURVE_JUMP;
const MP_CAPS MP_CAPS_CURVE_LINEAR = MP_CURVE_LINEAR;
const MP_CAPS MP_CAPS_CURVE_SQUARE = MP_CURVE_SQUARE;
const MP_CAPS MP_CAPS_CURVE_INVSQUARE = MP_CURVE_INVSQUARE;
const MP_CAPS MP_CAPS_CURVE_SINE = MP_CURVE_SINE;


//------------------------------------------------------------------------------
// Structure used to return information about the type and limits of a parameter
//------------------------------------------------------------------------------
typedef struct _MP_PARAMINFO {
   MP_TYPE mpType;      // One of MP_TYPE_xxx codes
   MP_CAPS  mopCaps;    // A collection of MP_CAPS flags

   // Minimum and maximum values
   MP_DATA mpdMinValue;     // minimum legal value
   MP_DATA mpdMaxValue;     // maximum legal value
   MP_DATA mpdNeutralValue; // default or 'center' value

   // Defualt Unit and Label text. These strings will ALWAYS be English
   // strings in the UNICODE character set. For international text
   // use the GetParamText member function
   WCHAR                   szUnitText[32];  // units of the parameter
   WCHAR                   szLabel[32];     // name of the parameter

} MP_PARAMINFO;


//------------------------------------------------------------------------------
// Parameter Index types
//------------------------------------------------------------------------------
typedef DWORD DWORD;
const DWORD DWORD_ALLPARAMS = -1;   // Apply this operation to all params


//------------------------------------------------------------------------------
// Defined list of timestamp types
//------------------------------------------------------------------------------
typedef DWORD MP_TIMEDATA;  // Extra data to further define type

// REFERENCE_TIME (1 tick = 100 nanoseconds, MP_TIMEDATA ignored)
cpp_quote("DEFINE_GUID(GUID_TIME_REFERENCE,")
cpp_quote("0x93ad712b, 0xdaa0, 0x4ffe, 0xbc, 0x81, 0xb0, 0xce, 0x50, 0xf, 0xcd, 0xd9);")

// Music Time (MP_TIMEDATA = parts/quarter note)
cpp_quote("DEFINE_GUID(GUID_TIME_MUSIC,")
cpp_quote("0x574c49d, 0x5b04, 0x4b15, 0xa5, 0x42, 0xae, 0x28, 0x20, 0x30, 0x11, 0x7b);")

// Time is measures in samples. MP_TIMEDATA = Samples/sec)
cpp_quote("DEFINE_GUID(GUID_TIME_SAMPLES,")
cpp_quote("0xa8593d05, 0xc43, 0x4984, 0x9a, 0x63, 0x97, 0xaf, 0x9e, 0x2, 0xc4, 0xc0);")


//------------------------------------------------------------------------------
// The value of a given parameter at a specific point in time
//------------------------------------------------------------------------------
typedef DWORD MP_FLAGS;
const MP_FLAGS MPF_ENVLP_STANDARD         = 0x0000; // Use all data provided
const MP_FLAGS MPF_ENVLP_BEGIN_CURRENTVAL = 0x0001;
        // Ignore valStart value, use current value as the staring point
const MP_FLAGS MPF_ENVLP_BEGIN_NEUTRALVAL = 0x0002;
        // Ignore valStart value, use neutral value as the staring point

typedef struct _MP_ENVELOPE_SEGMENT {
   REFERENCE_TIME  rtStart;     // Start time in current time format
   REFERENCE_TIME  rtEnd;       // End time in current time format
   MP_DATA         valStart;    // Initial Value
   MP_DATA         valEnd;      // Final Value
   MP_CURVE_TYPE   iCurve;      // One of MP_CURVE_TYPE codes
   MP_FLAGS        flags;       // Special cases
} MP_ENVELOPE_SEGMENT;

//------------------------------------------------------------------------------
// Define flags for Punch-in timing
//------------------------------------------------------------------------------
const MP_FLAGS MPF_PUNCHIN_REFTIME = 0; // Use the reference time as the PI time
const MP_FLAGS MPF_PUNCHIN_NOW = 0x0001; // Punch in at the current clock time
const MP_FLAGS MPF_PUNCHIN_STOPPED = 0x0002; // Return change notifications during
                                             // author time

//------------------------------------------------------------------------------
// IMediaParamInfo - Interface used to determine the names, data types and
// units of the parameters which are exposed by the object. This interface
// is used at discovery time, and is not required during run-time since the
// objects parameters are a fixed set and this data can be cached by the
// calling applicaiton
//------------------------------------------------------------------------------
[
object,
uuid(6d6cbb60-a223-44aa-842f-a2f06750be6d),
version(1.0)
]
interface IMediaParamInfo : IUnknown
{
    HRESULT GetParamCount (
        [out] DWORD * pdwParams
    );
    HRESULT GetParamInfo (
        [in] DWORD dwParamIndex,
        [out] MP_PARAMINFO * pInfo
    );
    // returns a series of null terminated strings. strings are in the
    // following order:
    // Param Label, Units Text, 1st Enum Text, 2nd Enum Text, etc...
    HRESULT GetParamText (
        [in] DWORD dwParamIndex,    // which param to get text for
        [out] WCHAR **ppwchText     // returns ptr to CoTaskMemAlloc'd string
    );

    // Returns the number of diffrent time formats this object understands
    HRESULT GetNumTimeFormats (
        [out] DWORD * pdwNumTimeFormats
    );

    // Returns the GUID for the ith supported time format
    HRESULT GetSupportedTimeFormat(
        [in] DWORD dwFormatIndex,
        [out] GUID *pguidTimeFormat
    );

    // Returns the current time format
    HRESULT GetCurrentTimeFormat (
        [out] GUID *pguidTimeFormat,
        [out] MP_TIMEDATA *pTimeData
    );
}

//------------------------------------------------------------------------------
// IMediaParams - Interfaes used to actually set the media params and the
// envelopes to follow
//------------------------------------------------------------------------------
[
object,
uuid(6d6cbb61-a223-44aa-842f-a2f06750be6e),
version(1.0)
]
interface IMediaParams : IUnknown
{
    // Single param Get/Set methods
    HRESULT GetParam (
        [in] DWORD dwParamIndex,
        [out] MP_DATA *pValue
    );
    HRESULT SetParam (
        [in] DWORD dwParamIndex,
        [in] MP_DATA value
    );

    // Envelope methods (param change over time)
    HRESULT AddEnvelope (
        [in] DWORD dwParamIndex,
        [in] DWORD cSegments,
        [in]  MP_ENVELOPE_SEGMENT * pEnvelopeSegments
    );

    // Flush all of the envelope information for the given paramter between
    // the timestamps specified
    HRESULT FlushEnvelope (
        [in] DWORD dwParamIndex,
        [in] REFERENCE_TIME refTimeStart,
        [in] REFERENCE_TIME refTimeEnd
    );

    // Change the time format being used by the object
    HRESULT SetTimeFormat (
        [in] GUID guidTimeFormat,
        [in] MP_TIMEDATA mpTimeData
    );
}



