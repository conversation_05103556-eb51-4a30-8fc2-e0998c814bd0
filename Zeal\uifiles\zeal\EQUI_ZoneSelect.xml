<?xml version="1.0" encoding="us-ascii"?>
<XML ID="EQInterfaceDefinitionLanguage">
  <Schema xmlns="EverQuestData" xmlns:dt="EverQuestDataTypes" />
  <Label item="Zeal_ZoneSelect_CurrentZone">
    <ScreenID>Zeal_ZoneSelect_CurrentZone</ScreenID>
    <Font>3</Font>
    <Text></Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <Size>
      <CX>220</CX>
      <CY>24</CY>
    </Size>
    <AlignCenter>true</AlignCenter>
    <Location>
      <X>4</X>
      <Y>4</Y>
    </Location>
    <AutoStretch>true</AutoStretch>
    <RelativePosition>true</RelativePosition>
    <RightAnchorToLeft>false</RightAnchorToLeft>
    <TopAnchorToTop>true</TopAnchorToTop>
    <LeftAnchorToLeft>true</LeftAnchorToLeft>
    <BottomAnchorToTop>false</BottomAnchorToTop>
	<TopAnchorOffset>4</TopAnchorOffset>
    <BottomAnchorOffset>4</BottomAnchorOffset>
  </Label>
  <Listbox item="Zeal_ZoneSelect_ListBox">
    <ScreenID>Zeal_ZoneSelect_ListBox</ScreenID>
    <DrawTemplate>WDT_Inner</DrawTemplate>
    <TooltipReference>List of Zones.</TooltipReference>
    <AutoStretch>true</AutoStretch>
    <LeftAnchorOffset>4</LeftAnchorOffset>
    <TopAnchorOffset>24</TopAnchorOffset>
    <RightAnchorOffset>4</RightAnchorOffset>
    <BottomAnchorOffset>44</BottomAnchorOffset>
    <TopAnchorToTop>true</TopAnchorToTop>
    <BottomAnchorToTop>false</BottomAnchorToTop>
    <LeftAnchorToLeft>true</LeftAnchorToLeft>
    <RightAnchorToLeft>false</RightAnchorToLeft>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <Style_Border>true</Style_Border>
    <Style_VScroll>true</Style_VScroll>
    <Columns>
      <Width>45</Width>
      <Heading>ID</Heading>
    </Columns>
    <Columns>
      <Width>100</Width>
      <Heading>Short Name</Heading>
    </Columns>
    <Columns>
      <Width>160</Width>
      <Heading>Long Name</Heading>
    </Columns>
   </Listbox>
   <Button item="Zeal_ZoneSelect_Apply">
    <ScreenID>Zeal_ZoneSelect_Apply</ScreenID>
    <RelativePosition>true</RelativePosition>
    <AutoStretch>true</AutoStretch>
    <LeftAnchorOffset>4</LeftAnchorOffset>
    <TopAnchorOffset>40</TopAnchorOffset>
    <RightAnchorOffset>100</RightAnchorOffset>
    <BottomAnchorOffset>4</BottomAnchorOffset>
    <TopAnchorToTop>false</TopAnchorToTop>
    <BottomAnchorToTop>false</BottomAnchorToTop>
    <LeftAnchorToLeft>true</LeftAnchorToLeft>
    <RightAnchorToLeft>true</RightAnchorToLeft>
    <Style_Checkbox>false</Style_Checkbox>
    <TooltipReference>Applies the character select zone</TooltipReference>
    <Text>Apply</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_SmallBtnNormal</Normal>
      <Pressed>A_SmallBtnPressed</Pressed>
      <Flyby>A_SmallBtnFlyby</Flyby>
      <Disabled>A_SmallBtnDisabled</Disabled>
      <PressedFlyby>A_SmallBtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  
  <Screen item="ZealZoneSelect">
    <RelativePosition>false</RelativePosition>
    <Location>
      <X>240</X>
      <Y>240</Y>
    </Location>
    <Size>
      <CX>310</CX>
      <CY>400</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <DrawTemplate>WDT_Def2</DrawTemplate>
    <Style_Titlebar>true</Style_Titlebar>
    <Style_Closebox>false</Style_Closebox>
    <Style_Minimizebox>false</Style_Minimizebox>
    <Style_Border>true</Style_Border>
    <Style_Sizable>true</Style_Sizable>
    <Pieces>Zeal_ZoneSelect_ListBox</Pieces>
	<Pieces>Zeal_ZoneSelect_Apply</Pieces>
	<Pieces>Zeal_ZoneSelect_CurrentZone</Pieces>
  </Screen>
</XML>