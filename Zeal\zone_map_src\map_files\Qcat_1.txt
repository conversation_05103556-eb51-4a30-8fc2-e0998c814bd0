L 299.6050, -358.9361, -41.9667, 299.6170, -352.6966, -41.9667, 240, 127, 0
P 63.3826, -229.5025, -56.8216, 255, 0, 0, 3, to_South_Qeynos_(Bank_Bridge)
P 304.1817, -214.9780, -38.2176, 255, 0, 0, 3, to_South_Qeynos_(Arena_Area)
P 43.5428, -1061.9877, -69.9656, 255, 0, 0, 3, to_North_Qeynos_(NE_Pool)
P -222.8664, -882.6959, -49.7612, 255, 0, 0, 3, to_North_Qeynos_(Crow`s_Bar)
P -96.7670, -638.0028, -38.2118, 255, 0, 0, 3, to_North_Qeynos_(Rogue`s_Guild)
P -130.9488, 144.5200, -52.2152, 255, 0, 0, 3, to_South_Qeynos_(Docks)
P 175.0000, -343.0000, -55.3508, 255, 0, 0, 3, to_North_Qeynos_(East)
P -80.0000, -860.0000, -41.9656, 240, 0, 0, 2, Succor
P -57.8030, -661.4808, -97.7062, 0, 0, 240, 1, GS:_<PERSON>
P -135.8107, -894.0803, -40.4326, 240, 240, 240, 2, Clumpy_the_Rat_<PERSON>
P -106.7977, -978.0188, -40.4288, 240, 240, 240, 2, Go_Back
P -65.0119, -922.0501, -40.4233, 240, 240, 240, 2, <PERSON>
P -75.9985, -827.2315, -40.4128, 240, 240, 240, 2, Shark
P -127.0823, -743.2924, -40.4100, 240, 240, 240, 2, The_Irontoe_Brigade_Rules_Norrath
P -200.0000, -275.0000, 0.0000, 255, 255, 255, 2, Sewer_Sentinels
P 30.0000, -100.0000, 0.0000, 255, 255, 255, 2, Frogloks
P -345.0000, -465.0000, 0.0000, 255, 255, 255, 2, Mercenaries
P 220.0000, -445.0000, 0.0000, 255, 255, 255, 2, Spectres
P 320.0000, -355.0000, 0.0000, 255, 255, 255, 2, Bertoxxulous_Shrine
P -330.0000, -340.0000, 0.0000, 255, 255, 255, 2, Zombies
P -65.0000, -660.0000, 0.0000, 255, 255, 255, 2, Sharks
P -199.7185, -455.0576, -41.9666, 240, 240, 240, 2, Spawn_Hall
P 60.7392, -847.2912, -83.7062, 240, 240, 240, 2, Smugglers
P 131.3856, -748.4018, -83.7062, 240, 240, 240, 2, Smugglers
P 160.9217, -496.4899, -83.7062, 240, 240, 240, 2, Thugs
P 202.7369, -372.4356, -83.7062, 240, 240, 240, 2, Smugglers
P -21.2864, -665.3104, -41.7062, 240, 240, 0, 2, TRAP:_Fake_Floor
P -63.9994, -714.4289, -41.7062, 240, 240, 0, 2, Fake_Wall
P -82.7131, -693.4394, -41.7062, 240, 240, 0, 2, Fake_Wall
P -91.3770, -664.2468, -41.7062, 240, 240, 0, 2, TRAP:_Fake_Floor
P -62.4495, -767.8248, -41.7062, 240, 240, 0, 2, Fake_Wall
P 20.5789, -621.0987, -41.7062, 240, 240, 0, 2, Fake_Wall
P -9.1884, -521.5432, -41.7062, 240, 240, 0, 2, Fake_Wall
P -66.4798, -455.2566, -41.7062, 240, 240, 0, 2, Fake_Wall
P 440.6667, -411.2162, -41.9666, 0, 128, 0, 2, Felia_Milltorn_(General)
P 437.8191, -422.1060, -41.9666, 0, 128, 0, 2, Gehnon_Dejartes_(Spells)
P 418.8909, -397.6574, -41.9666, 0, 128, 0, 2, Jexun_Elsorg_(Spells)
P 436.7461, -339.9577, -38.2176, 0, 128, 0, 2, Tessia_Sowtsui_(Spells)
P 426.7759, -339.8430, -41.9666, 0, 128, 0, 2, Naes_Ereek_(Spells)
P 412.9024, -345.6535, -38.2176, 0, 128, 0, 2, Senst_Ereek_(Spells)
P 370.8585, -429.3848, -39.4676, 128, 128, 128, 2, Zaen_(GM_Shadowknight)
P 298.0000, -296.0000, -39.4679, 128, 128, 128, 2, Kurne_Rextula_(GM_Necromancer)
P 301.3531, -418.9998, -41.9666, 127, 127, 127, 2, Sragg_Bloodheart_(GM_Shadowknight)
P 308.8775, -403.3051, -41.9666, 0, 127, 0, 2, Dallisen_Xalladyr_(Spells)
P 320.7840, -398.4042, -41.9666, 0, 127, 0, 2, Felweni_the_Kraven_(Spells)
P 353.6126, -396.9795, -41.9666, 127, 127, 127, 2, Perkon_Malok_(GM_Magician)
P 385.9821, -392.2093, -41.9666, 127, 127, 127, 2, Reania_Jukle_(GM_Enchanter)
P 420.5082, -426.8678, -41.9666, 127, 127, 127, 2, Unmuz_Tukore_(GM_Shadowkinght)
P 431.0758, -394.4562, -41.9666, 0, 127, 0, 2, Dranot_Harbin_(General)
P 380.9879, -348.8858, -41.9666, 127, 127, 127, 2, Ranthor_Bekesna_(GM_Warrior)
P 386.0470, -323.7961, -41.9666, 127, 127, 127, 2, Rocthar_Bekesna_(GM_Warrior)
P 354.2517, -322.5526, -41.9666, 127, 127, 127, 2, Trenon_Callust_(GM_Wizard)
P 383.8066, -277.3405, -41.9666, 0, 127, 0, 2, Kubder_Restona_(General)
P 421.1775, -282.9201, -41.9666, 127, 127, 127, 2, Bruax_Grengar_(GM_Necromancer)
P 445.1982, -300.0023, -41.9666, 127, 127, 127, 2, Lyris_Moonbane_(GM_Necromancer)
P 321.9149, -310.5846, -41.9666, 0, 127, 0, 2, Pakeg_Aspet_(Spells)
P 311.9761, -308.5765, -41.9666, 0, 127, 0, 2, Vidurlyn_Aeminee_(Spells)
P 310.9863, -291.1543, -41.9666, 127, 127, 127, 2, Nulima_Opalis_(GM_Necromancer)
P 323.6325, -285.8139, -41.9666, 0, 127, 0, 2, Gyrna_Porrin_(Spells)
P 416.2501, -375.2278, -41.9666, 0, 127, 0, 2, Likka_Sowtsui_(Spells)
P 434.7038, -369.0446, -41.9666, 0, 127, 0, 2, Leon_Ereek_(Spells)
P 452.7906, -357.7529, -41.9666, 127, 127, 127, 2, Rihtur_Fullome_(GM_Cleric)
P 538.2132, -353.1490, -41.9666, 127, 127, 127, 2, Rhes_Gestolk_(GM_Cleric)
P 651.1842, -357.8692, -38.9979, 127, 127, 127, 2, Garuc_Anehm_(GM_Shadowknight)
P 620.7598, -358.6145, -38.9979, 127, 127, 127, 2, Xenture_Demiagar_(GM_Cleric)
P 619.7509, -345.8688, -38.9979, 127, 127, 127, 2, Nomaria_Doseniar_(GM_Cleric)
P 324.7449, -423.7232, -41.9666, 255, 210, 0, 2, Banker_Javen_(Banker)
P 561.7280, -378.4159, -41.9666, 0, 127, 0, 2, Vach_Piral_(Tribute)
P -140.0000, 45.0000, 0.0000, 0, 0, 0, 2, Exhausted_Guard
P 186.0000, -106.0000, 0.0000, 0, 0, 0, 2, Injured_Brigand
P 103.7443, -556.4523, -83.7062, 0, 0, 0, 2, Beggar_Wyllin
P -19.6412, -958.7804, -41.7062, 0, 0, 0, 2, an_investigator
P 338.7951, -375.2062, -41.9666, 0, 0, 0, 2, Kron_Redstepp
P 340.8662, -338.1529, -41.9666, 0, 0, 0, 2, Morn_Darkson
P 352.4880, -286.4691, -41.9666, 0, 0, 0, 2, Torin_Krentar
P 367.7274, -271.1975, -41.9666, 0, 0, 0, 2, Chan_Whinstone
P 385.0655, -309.4481, -41.9666, 0, 0, 0, 2, Lahn_Whinstone
P 406.7928, -339.9501, -41.9666, 0, 0, 0, 2, Teydar
P 538.4423, -334.2605, -41.9666, 0, 0, 0, 2, Illie_Roln
P 538.0144, -374.7740, -41.9666, 0, 0, 0, 2, Wellis_Pestule
P 575.9452, -399.6638, -40.9666, 0, 0, 0, 2, Van_Shatblack
P 590.6004, -406.3757, -39.9666, 0, 0, 0, 2, Dollin_Nusmag
P 519.2550, -395.4198, -41.9666, 0, 0, 0, 2, Commander_Kane_(Roam)
P 62.9522, -174.2726, -40.4394, 0, 0, 0, 2, Cubert_(Roam)
P -230.0000, -650.0000, 0.0000, 0, 0, 0, 2, Bloated_Alligator
P -55.3019, -696.9531, -83.7062, 0, 0, 0, 2, Omorb
P -68.1621, -696.6344, -83.7062, 0, 0, 0, 2, Neab
P -208.6407, -573.0528, -41.6541, 127, 64, 0, 2, an_injured_rat_(Hunter)
P -328.9900, -344.0253, -41.6541, 127, 64, 0, 2, a_nesting_rat_(Hunter)
P -342.5141, -465.8772, -41.6541, 127, 64, 0, 2, _a_shady_mercenary_(Hunter)
P -148.2299, -667.0105, -54.4581, 127, 64, 0, 2, an_undead_knight_(Hunter)
P -10.0000, 55.0000, 0.0000, 127, 64, 0, 2, an_undead_knight_(Hunter)
P -34.2146, -777.6966, -40.4486, 0, 0, 0, 2, Ronn_Castekin_(Roam)
P 323.2138, -354.9571, -41.9667, 240, 240, 240, 2, Temple_of_Bertoxxulous
