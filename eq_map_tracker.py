#!/usr/bin/env python3
"""
EverQuest Map Tracker
---------------------
A Python application that hooks into EverQuest to track zones,
create visual maps, and show real-time mob locations with names and levels.
"""

import sys
import time
import ctypes
import struct
import tkinter as tk
from tkinter import ttk, messagebox
from dataclasses import dataclass
import win32gui
import win32process
import win32api
import win32con
import psutil

# Constants from Zeal codebase
PROCESS_VM_READ = 0x0010
PROCESS_QUERY_INFORMATION = 0x0400

# Memory addresses from Zeal\EqAddresses.h
SELF_PTR_ADDR = 0x7F94CC
TARGET_PTR_ADDR = 0x7F94EC
ENTITY_LIST_PTR_ADDR = 0x7f9498
ZONE_INFO_ADDR = 0x00798784

# Entity type constants from Zeal\EqStructures.h
ENTITY_TYPE_PLAYER = 0
ENTITY_TYPE_NPC = 1
ENTITY_TYPE_NPC_CORPSE = 2
ENTITY_TYPE_PLAYER_CORPSE = 3
ENTITY_TYPE_UNKNOWN = 4

# Data structures
@dataclass
class Position:
    x: float
    y: float
    z: float

@dataclass
class Entity:
    id: int
    name: str
    level: int
    position: Position
    type: str  # 'player', 'npc', etc.

class MemoryReader:
    """Class to read EverQuest memory"""

    def __init__(self):
        self.process_handle = None
        self.process_id = None
        self.base_address = None
        self.connected = False

    def connect_to_eq(self):
        """Find and connect to the EverQuest process"""
        eq_process = None

        # Look for EverQuest process
        for proc in psutil.process_iter(['pid', 'name']):
            if proc.info['name'] and 'eqgame' in proc.info['name'].lower():
                eq_process = proc
                break

        if not eq_process:
            return False

        self.process_id = eq_process.info['pid']

        # Get process handle with read access
        self.process_handle = ctypes.windll.kernel32.OpenProcess(
            PROCESS_VM_READ | PROCESS_QUERY_INFORMATION,
            False,
            self.process_id
        )

        if not self.process_handle:
            return False

        # Get base address of the process
        modules = win32process.EnumProcessModules(self.process_handle)
        self.base_address = modules[0]  # Main module

        self.connected = True
        return True

    def read_memory(self, address, data_type):
        """Read memory at the specified address with the given data type"""
        if not self.connected:
            return None

        buffer = ctypes.create_string_buffer(struct.calcsize(data_type))
        bytes_read = ctypes.c_ulong(0)

        result = ctypes.windll.kernel32.ReadProcessMemory(
            self.process_handle,
            address,
            buffer,
            struct.calcsize(data_type),
            ctypes.byref(bytes_read)
        )

        if result and bytes_read.value == struct.calcsize(data_type):
            return struct.unpack(data_type, buffer.raw)[0]
        return None

    def read_string(self, address, max_length=50):
        """Read a null-terminated string from memory"""
        if not self.connected:
            return ""

        buffer = ctypes.create_string_buffer(max_length)
        bytes_read = ctypes.c_ulong(0)

        result = ctypes.windll.kernel32.ReadProcessMemory(
            self.process_handle,
            address,
            buffer,
            max_length,
            ctypes.byref(bytes_read)
        )

        if result:
            try:
                return buffer.value.decode('utf-8').split('\0')[0]
            except:
                return ""
        return ""

    def get_current_zone(self, display = 1):
        """Get the current zone name from ZONE_INFO_ADDR"""
        # Based on EQZONEINFO structure in EqStructures.h
        # ShortName is at offset 0x40, LongName at 0x60
        zone_info_ptr = self.read_memory(ZONE_INFO_ADDR, 'I')
        if not zone_info_ptr:
            return "Unknown Zone"

        # Read the zone short name (offset 0x40, length 0x20)
        short_name_addr = ZONE_INFO_ADDR + 0x40
        short_name = self.read_string(short_name_addr, 0x20)

        # Read the zone long name (offset 0x60, length 0x80)
        long_name_addr = ZONE_INFO_ADDR + 0x60
        long_name = self.read_string(long_name_addr, 0x80)

        if display == 1:
            return long_name if long_name else short_name
        else:
            return short_name

    def get_player_position(self):
        """Get the player's current position"""
        # Read the self entity pointer
        self_ptr = self.read_memory(SELF_PTR_ADDR, 'I')
        if not self_ptr:
            return Position(0, 0, 0)

        # Position is at offset 0x48 in the Entity structure
        # It's a Vec3 structure with x, y, z as floats
        pos_x = self.read_memory(self_ptr + 0x48, 'f')
        pos_y = self.read_memory(self_ptr + 0x48 + 4, 'f')
        pos_z = self.read_memory(self_ptr + 0x48 + 8, 'f')

        if pos_x is None or pos_y is None or pos_z is None:
            return Position(0, 0, 0)

        return Position(pos_x, pos_y, pos_z)

    def get_entities(self):
        """Get all entities (mobs, players) in the current zone"""
        entities = []

        # Read the entity list pointer
        entity_list_ptr = self.read_memory(ENTITY_LIST_PTR_ADDR, 'I')
        if not entity_list_ptr:
            return entities

        # Traverse the linked list of entities
        current_entity = entity_list_ptr
        while current_entity:
            # Check if the entity is valid (StructType at offset 0x0 should be 0x03)
            struct_type = self.read_memory(current_entity, 'B')
            if struct_type != 0x03:
                # Move to next entity
                next_entity = self.read_memory(current_entity + 0x7C, 'I')  # Next is at offset 0x7C
                current_entity = next_entity
                continue

            # Read entity data
            name = self.read_string(current_entity + 0x01, 30)  # Name is at offset 0x01, length 30
            spawn_id = self.read_memory(current_entity + 0x94, 'H')  # SpawnId at offset 0x94
            level = self.read_memory(current_entity + 0xAD, 'B')  # Level at offset 0xAD
            entity_type = self.read_memory(current_entity + 0xA8, 'B')  # Type at offset 0xA8

            # Read position (offset 0x48)
            pos_x = self.read_memory(current_entity + 0x48, 'f')
            pos_y = self.read_memory(current_entity + 0x48 + 4, 'f')
            pos_z = self.read_memory(current_entity + 0x48 + 8, 'f')

            # Determine entity type string
            type_str = 'unknown'
            if entity_type == ENTITY_TYPE_PLAYER:
                type_str = 'player'
            elif entity_type == ENTITY_TYPE_NPC:
                type_str = 'npc'
            elif entity_type == ENTITY_TYPE_NPC_CORPSE:
                type_str = 'npc_corpse'
            elif entity_type == ENTITY_TYPE_PLAYER_CORPSE:
                type_str = 'player_corpse'

            # Create entity object
            entity = Entity(
                id=spawn_id,
                name=name,
                level=level,
                position=Position(pos_x, pos_y, pos_z),
                type=type_str
            )

            # Add to list if it's a valid entity
            if name and spawn_id:
                entities.append(entity)

            # Move to next entity
            next_entity = self.read_memory(current_entity + 0x7C, 'I')  # Next is at offset 0x7C
            current_entity = next_entity

        return entities

    def close(self):
        """Close the process handle"""
        if self.process_handle:
            ctypes.windll.kernel32.CloseHandle(self.process_handle)
            self.process_handle = None
            self.connected = False

from zone_map_loader import ZoneMapLoader

class MapRenderer:
    """Class to render the zone map and entities"""

    def __init__(self, canvas, width, height):
        self.canvas = canvas
        self.width = width
        self.height = height
        self.scale = 1.0
        self.offset_x = width / 2
        self.offset_y = height / 2
        self.entity_markers = {}
        self.show_mob_levels = True  # Flag to toggle mob level display
        self.show_zone_map = True  # Flag to toggle zone map display
        self.show_corpses = True  # Flag to toggle corpse display
        self.zoom_scroll_lock = False  # Flag to disable mouse wheel zooming
        self.current_zone_name = None
        self.current_zone_map = None
        self.map_loader = ZoneMapLoader()
        self.current_level = 0  # Default to main level

    def clear(self):
        """Clear the canvas"""
        self.canvas.delete("all")
        self.entity_markers = {}

    def draw_grid(self, spacing=50):
        """Draw a grid on the canvas"""
        # Vertical lines
        for x in range(0, self.width, spacing):
            self.canvas.create_line(x, 0, x, self.height, fill="#333333")

        # Horizontal lines
        for y in range(0, self.height, spacing):
            self.canvas.create_line(0, y, self.width, y, fill="#333333")

    def world_to_screen(self, x, y):
        """Convert world coordinates to screen coordinates"""
        # In EverQuest, Y and X are swapped and Y is negated for display
        screen_x = -y * self.scale + self.offset_x
        screen_y = -x * self.scale + self.offset_y
        return screen_x, screen_y

    def load_zone_map(self, zone_name):
        """Load the zone map for the given zone name"""
        if zone_name != self.current_zone_name:
            self.current_zone_map = self.map_loader.load_zone_map(zone_name.lower())
            self.current_zone_name = zone_name

            # Auto-adjust scale and offset for the new map
            if self.current_zone_map:
                self.auto_adjust_view()

        return self.current_zone_map

    def auto_adjust_view(self):
        """Automatically adjust the view to fit the zone map"""
        if not self.current_zone_map:
            return

        # Calculate the scale to fit the map in the canvas
        map_width = self.current_zone_map.max_y - self.current_zone_map.min_y
        map_height = self.current_zone_map.max_x - self.current_zone_map.min_x

        if map_width <= 0 or map_height <= 0:
            return

        # Calculate scale to fit the map in the canvas with some padding
        scale_x = (self.width * 0.9) / map_width
        scale_y = (self.height * 0.9) / map_height
        self.scale = min(scale_x, scale_y)

        # Center the map in the canvas
        center_x = (self.current_zone_map.max_y + self.current_zone_map.min_y) / 2
        center_y = (self.current_zone_map.max_x + self.current_zone_map.min_x) / 2
        self.offset_x = self.width / 2 + center_x * self.scale
        self.offset_y = self.height / 2 + center_y * self.scale

    def draw_zone_map(self):
        """Draw the zone map on the canvas"""
        if not self.current_zone_map or not self.show_zone_map:
            self.draw_grid()
            return

        # Draw map lines for the current level
        for line in self.current_zone_map.lines:
            # Skip lines that are not on the current level
            if self.current_level != 0 and line.level_id != self.current_level:
                continue

            x0, y0 = self.world_to_screen(line.x0, line.y0)
            x1, y1 = self.world_to_screen(line.x1, line.y1)

            # Convert RGB to hex color
            color = f"#{line.red:02x}{line.green:02x}{line.blue:02x}"

            self.canvas.create_line(
                x0, y0, x1, y1,
                fill=color,
                width=1,
                tags="map_line"
            )

        # Draw map labels
        for label in self.current_zone_map.labels:
            x, y = self.world_to_screen(label.x, label.y)

            # Convert RGB to hex color
            color = f"#{label.red:02x}{label.green:02x}{label.blue:02x}"

            self.canvas.create_text(
                x, y,
                text=label.text,
                fill=color,
                font=("Arial", 8),
                tags="map_label"
            )

    def draw_player(self, position):
        """Draw the player on the map"""
        x, y = self.world_to_screen(position.x, position.y)
        size = 10
        self.canvas.create_oval(
            x - size, y - size, x + size, y + size,
            fill="yellow", outline="black", tags="player"
        )
        self.canvas.create_text(
            x, y - size - 10,
            text="YOU",
            fill="yellow", font=("Arial", 10, "bold"), tags="player_text"
        )

    def draw_entity(self, entity, is_filtered=False):
        """Draw an entity on the map"""
        x, y = self.world_to_screen(entity.position.x, entity.position.y)

        # Different colors for different entity types
        if entity.type == 'npc':
            color = "red"
            size = 5
            # Highlight filtered mobs
            if is_filtered:
                color = "orange"  # Brighter color for filtered mobs
                size = 8  # Larger size for filtered mobs
        elif entity.type == 'player':
            color = "blue"
            size = 7
        elif entity.type == 'npc_corpse':
            color = "gray"
            size = 4
        elif entity.type == 'player_corpse':
            color = "purple"
            size = 6
        else:
            color = "white"
            size = 5

        # Create entity label
        label_text = entity.name
        if self.show_mob_levels and entity.type == 'npc':
            label_text = f"{entity.name}({entity.level})"

        # Text color - highlight filtered mobs
        text_color = "white"
        font_style = ("Arial", 8)
        if is_filtered:
            text_color = "yellow"  # Brighter text for filtered mobs
            font_style = ("Arial", 9, "bold")  # Bold text for filtered mobs

        # Create or update entity marker
        if entity.id in self.entity_markers:
            marker_id, text_id = self.entity_markers[entity.id]
            self.canvas.coords(marker_id, x - size, y - size, x + size, y + size)
            self.canvas.itemconfig(marker_id, fill=color)
            self.canvas.coords(text_id, x, y - size - 10)
            self.canvas.itemconfig(text_id, text=label_text, fill=text_color, font=font_style)
        else:
            marker_id = self.canvas.create_oval(
                x - size, y - size, x + size, y + size,
                fill=color, outline="black", tags=f"entity_{entity.id}"
            )
            text_id = self.canvas.create_text(
                x, y - size - 10,
                text=label_text,
                fill=text_color, font=font_style, tags=f"entity_text_{entity.id}"
            )
            self.entity_markers[entity.id] = (marker_id, text_id)

    def toggle_mob_levels(self):
        """Toggle the display of mob levels"""
        self.show_mob_levels = not self.show_mob_levels
        return self.show_mob_levels

    def toggle_zone_map(self):
        """Toggle the display of the zone map"""
        self.show_zone_map = not self.show_zone_map
        return self.show_zone_map

    def toggle_corpses(self):
        """Toggle the display of corpses"""
        self.show_corpses = not self.show_corpses
        return self.show_corpses

    def toggle_zoom_scroll_lock(self):
        """Toggle the zoom scroll lock (whether mouse wheel zooms)"""
        self.zoom_scroll_lock = not self.zoom_scroll_lock
        return self.zoom_scroll_lock

    def set_level(self, level):
        """Set the current level to display"""
        self.current_level = level

    def update(self, zone_name, player_position, entities, short_zone_name, mob_filter=""):
        """Update the map with current player and entity positions"""
        self.clear()

        # Load the zone map if needed
        self.load_zone_map(short_zone_name)

        # Draw the zone map or grid
        self.draw_zone_map()

        # Draw the player
        self.draw_player(player_position)

        # Filter entities based on settings
        filtered_entities = []
        for entity in entities:
            # Skip corpses if show_corpses is False
            if not self.show_corpses and 'corpse' in entity.type:
                continue

            # Skip corpses if there are too many entities (over 100)
            if len(entities) > 100 and 'corpse' in entity.type:
                continue

            filtered_entities.append(entity)

        # Draw the entities
        for entity in filtered_entities:
            # Check if this entity matches the filter
            is_filtered = False
            if mob_filter and entity.type == 'npc' and mob_filter in entity.name.lower():
                is_filtered = True

            self.draw_entity(entity, is_filtered)

class Application(tk.Tk):
    """Main application class"""

    def __init__(self):
        super().__init__()

        self.title("EverQuest Map Tracker")
        self.geometry("800x600")

        self.memory_reader = MemoryReader()
        self.create_widgets()

        self.update_interval = 1000  # Update every 1000ms (1 second)
        self.running = False
        self.mob_filter = ""  # Current mob filter string

        # Set up keyboard shortcuts
        self.bind("<KeyPress-m>", self.toggle_mob_levels)
        self.bind("<KeyPress-z>", self.toggle_zone_map)
        self.bind("<KeyPress-c>", self.toggle_corpses)
        self.bind("<KeyPress-s>", self.toggle_zoom_scroll_lock)
        self.bind("<KeyPress-l>", self.cycle_level)
        self.bind("<KeyPress-plus>", self.handle_keyboard_zoom_in)
        self.bind("<KeyPress-minus>", self.handle_keyboard_zoom_out)
        self.bind("<KeyPress-f>", self.focus_filter)

    def create_widgets(self):
        """Create the application widgets"""
        # Main frame
        main_frame = ttk.Frame(self)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Top frame for controls
        top_frame = ttk.Frame(main_frame)
        top_frame.pack(fill=tk.X, pady=(0, 10))

        # Connection status
        self.status_var = tk.StringVar(value="Not Connected")
        status_label = ttk.Label(top_frame, text="Status:")
        status_label.pack(side=tk.LEFT, padx=(0, 5))
        status_value = ttk.Label(top_frame, textvariable=self.status_var)
        status_value.pack(side=tk.LEFT, padx=(0, 20))

        # Zone info
        self.zone_var = tk.StringVar(value="Unknown")
        zone_label = ttk.Label(top_frame, text="Zone:")
        zone_label.pack(side=tk.LEFT, padx=(0, 5))
        zone_value = ttk.Label(top_frame, textvariable=self.zone_var)
        zone_value.pack(side=tk.LEFT, padx=(0, 20))

        # Entity count
        self.entity_count_var = tk.StringVar(value="Entities: 0")
        entity_count_label = ttk.Label(top_frame, textvariable=self.entity_count_var)
        entity_count_label.pack(side=tk.LEFT, padx=(0, 20))

        # Show mob levels checkbox
        self.show_mob_levels_var = tk.BooleanVar(value=True)
        show_mob_levels_cb = ttk.Checkbutton(
            top_frame,
            text="Show Mob Levels (M)",
            variable=self.show_mob_levels_var,
            command=self.toggle_mob_levels
        )
        show_mob_levels_cb.pack(side=tk.LEFT, padx=(0, 10))

        # Show zone map checkbox
        self.show_zone_map_var = tk.BooleanVar(value=True)
        show_zone_map_cb = ttk.Checkbutton(
            top_frame,
            text="Show Zone Map (Z)",
            variable=self.show_zone_map_var,
            command=self.toggle_zone_map
        )
        show_zone_map_cb.pack(side=tk.LEFT, padx=(0, 10))

        # Show corpses checkbox
        self.show_corpses_var = tk.BooleanVar(value=True)
        show_corpses_cb = ttk.Checkbutton(
            top_frame,
            text="Show Corpses (C)",
            variable=self.show_corpses_var,
            command=self.toggle_corpses
        )
        show_corpses_cb.pack(side=tk.LEFT, padx=(0, 10))

        # Zoom scroll lock checkbox
        self.zoom_scroll_lock_var = tk.BooleanVar(value=False)
        zoom_scroll_lock_cb = ttk.Checkbutton(
            top_frame,
            text="Zoom Scroll Lock (S)",
            variable=self.zoom_scroll_lock_var,
            command=self.toggle_zoom_scroll_lock
        )
        zoom_scroll_lock_cb.pack(side=tk.LEFT, padx=(0, 10))

        # Cycle level button
        cycle_level_btn = ttk.Button(
            top_frame,
            text="Cycle Level (L)",
            command=self.cycle_level
        )
        cycle_level_btn.pack(side=tk.LEFT, padx=(0, 10))

        # Connect button
        self.connect_button = ttk.Button(
            top_frame, text="Connect to EQ", command=self.toggle_connection
        )
        self.connect_button.pack(side=tk.RIGHT)

        # Bottom frame for controls
        bottom_frame = ttk.Frame(main_frame)
        bottom_frame.pack(fill=tk.X, pady=(10, 0))

        # Mob filter controls
        filter_label = ttk.Label(bottom_frame, text="Filter Mobs:")
        filter_label.pack(side=tk.LEFT, padx=(0, 5))

        self.mob_filter_var = tk.StringVar()
        self.mob_filter_var.trace_add("write", self.on_mob_filter_changed)
        mob_filter_entry = ttk.Entry(bottom_frame, textvariable=self.mob_filter_var, width=20)
        mob_filter_entry.pack(side=tk.LEFT, padx=(0, 5))

        clear_filter_btn = ttk.Button(bottom_frame, text="Clear", command=self.clear_mob_filter)
        clear_filter_btn.pack(side=tk.LEFT, padx=(0, 15))

        # Zoom controls
        zoom_in_btn = ttk.Button(bottom_frame, text="Zoom In (+)", command=self.handle_keyboard_zoom_in)
        zoom_in_btn.pack(side=tk.LEFT, padx=(0, 5))

        zoom_out_btn = ttk.Button(bottom_frame, text="Zoom Out (-)", command=self.handle_keyboard_zoom_out)
        zoom_out_btn.pack(side=tk.LEFT, padx=(0, 5))

        # Reset view button
        reset_view_btn = ttk.Button(bottom_frame, text="Reset View", command=self.reset_view)
        reset_view_btn.pack(side=tk.LEFT, padx=(0, 5))

        # Status bar
        self.status_bar = ttk.Label(main_frame, text="Press 'M' to toggle mob levels, 'Z' to toggle zone map, 'C' to toggle corpses, 'S' for zoom scroll lock, 'L' to cycle levels, 'F' to focus filter, '+'/'-' to zoom")
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X, pady=(5, 0))

        # Canvas for map
        self.canvas = tk.Canvas(main_frame, bg="black")
        self.canvas.pack(fill=tk.BOTH, expand=True)

        # Create map renderer
        self.map_renderer = MapRenderer(self.canvas, 800, 500)

        # Set up canvas drag for panning
        self.canvas.bind("<ButtonPress-1>", self.start_pan)
        self.canvas.bind("<B1-Motion>", self.pan)
        self.canvas.bind("<MouseWheel>", self.zoom_wheel)

    def start_pan(self, event):
        """Start panning the map"""
        self.canvas.scan_mark(event.x, event.y)

    def pan(self, event):
        """Pan the map"""
        self.canvas.scan_dragto(event.x, event.y, gain=1)

    def zoom_wheel(self, event):
        """Zoom with mouse wheel (only if zoom scroll lock is off)"""
        # Check if zoom scroll lock is enabled
        if self.map_renderer.zoom_scroll_lock:
            return  # Do nothing if zoom scroll lock is enabled

        # Get the mouse position
        mouse_x = event.x
        mouse_y = event.y

        # Calculate the zoom factor
        zoom_factor = 1.2 if event.delta > 0 else 1.0 / 1.2

        # Zoom at the mouse position
        self.zoom_at_point(mouse_x, mouse_y, zoom_factor)

    def zoom_at_point(self, x, y, zoom_factor):
        """Zoom at a specific point (mouse position)"""
        # Get the map renderer
        renderer = self.map_renderer

        # Calculate world coordinates before zoom
        old_scale = renderer.scale

        # Calculate the offset from the center of the screen to the mouse position
        dx = x - renderer.offset_x
        dy = y - renderer.offset_y

        # Apply the zoom factor
        renderer.scale *= zoom_factor

        # Adjust the offset to keep the point under the mouse in the same position
        renderer.offset_x = x - dx * (renderer.scale / old_scale)
        renderer.offset_y = y - dy * (renderer.scale / old_scale)

        # Update the map
        self.update_map()

    def zoom_in(self, event=None):
        """Zoom in on the map"""
        if event and hasattr(event, 'x') and hasattr(event, 'y'):
            # If called from a mouse event, zoom at mouse position
            self.zoom_at_point(event.x, event.y, 1.2)
        else:
            # Otherwise zoom at center
            center_x = self.canvas.winfo_width() / 2
            center_y = self.canvas.winfo_height() / 2
            self.zoom_at_point(center_x, center_y, 1.2)

    def zoom_out(self, event=None):
        """Zoom out on the map"""
        if event and hasattr(event, 'x') and hasattr(event, 'y'):
            # If called from a mouse event, zoom at mouse position
            self.zoom_at_point(event.x, event.y, 1.0 / 1.2)
        else:
            # Otherwise zoom at center
            center_x = self.canvas.winfo_width() / 2
            center_y = self.canvas.winfo_height() / 2
            self.zoom_at_point(center_x, center_y, 1.0 / 1.2)

    def reset_view(self):
        """Reset the map view"""
        self.map_renderer.scale = 1.0
        self.map_renderer.offset_x = self.canvas.winfo_width() / 2
        self.map_renderer.offset_y = self.canvas.winfo_height() / 2
        self.update_map()

    def toggle_mob_levels(self, event=None):
        """Toggle the display of mob levels"""
        if event:  # Called from keyboard shortcut
            self.show_mob_levels_var.set(not self.show_mob_levels_var.get())

        is_showing = self.map_renderer.toggle_mob_levels()
        self.show_mob_levels_var.set(is_showing)
        self.update_map()

    def update_map(self):
        """Update the map display with current data"""
        if not self.running:
            return

        try:
            # Get current zone
            zone = self.memory_reader.get_current_zone()

            # Get player position
            player_position = self.memory_reader.get_player_position()

            # Get entities
            entities = self.memory_reader.get_entities()

            # Apply mob filter if one is set
            if self.mob_filter:
                filtered_entities = []
                for entity in entities:
                    # Include the entity if it's not an NPC (always show players)
                    # or if its name contains the filter string
                    if entity.type != 'npc' or self.mob_filter in entity.name.lower():
                        filtered_entities.append(entity)
                entities = filtered_entities

            # Update entity count
            total_entities = len(self.memory_reader.get_entities())
            if self.mob_filter:
                self.entity_count_var.set(f"Entities: {len(entities)}/{total_entities} (filtered)")
            else:
                self.entity_count_var.set(f"Entities: {total_entities}")

            # Update map with zone name, player position, entities, and mob filter
            self.map_renderer.update(zone, player_position, entities, self.memory_reader.get_current_zone(0), self.mob_filter)
        except Exception as e:
            messagebox.showerror("Error", f"An error updating map: {str(e)}")

    def toggle_connection(self):
        """Toggle connection to EverQuest"""
        if not self.running:
            # Try to connect
            if self.memory_reader.connect_to_eq():
                self.status_var.set("Connected")
                self.connect_button.config(text="Disconnect")
                self.running = True
                self.update_data()
            else:
                messagebox.showerror(
                    "Connection Error",
                    "Could not connect to EverQuest. Make sure the game is running."
                )
        else:
            # Disconnect
            self.memory_reader.close()
            self.status_var.set("Not Connected")
            self.connect_button.config(text="Connect to EQ")
            self.running = False

    def update_data(self):
        """Update data from EverQuest and refresh the display"""
        if not self.running:
            return

        try:
            # Get current zone
            zone = self.memory_reader.get_current_zone()
            self.zone_var.set(zone)

            # Update the map
            self.update_map()

            # Schedule next update
            self.after(self.update_interval, self.update_data)
        except Exception as e:
            messagebox.showerror("Error", f"An error occurred: {str(e)}")
            self.toggle_connection()  # Disconnect on error

    def toggle_zone_map(self, event=None):
        """Toggle the display of the zone map"""
        if event:  # Called from keyboard shortcut
            is_showing = self.map_renderer.toggle_zone_map()
            self.update_map()
            return is_showing

    def toggle_corpses(self, event=None):
        """Toggle the display of corpses"""
        if event:  # Called from keyboard shortcut
            self.show_corpses_var.set(not self.show_corpses_var.get())

        is_showing = self.map_renderer.toggle_corpses()
        self.show_corpses_var.set(is_showing)
        self.update_map()

        # Update status bar
        if is_showing:
            self.status_bar.config(text="Corpses: Showing")
        else:
            self.status_bar.config(text="Corpses: Hidden")

        return is_showing

    def toggle_zoom_scroll_lock(self, event=None):
        """Toggle the zoom scroll lock (whether mouse wheel zooms)"""
        if event:  # Called from keyboard shortcut
            self.zoom_scroll_lock_var.set(not self.zoom_scroll_lock_var.get())

        is_locked = self.map_renderer.toggle_zoom_scroll_lock()
        self.zoom_scroll_lock_var.set(is_locked)

        # Update status bar
        if is_locked:
            self.status_bar.config(text="Zoom Scroll Lock: ON (Mouse wheel disabled)")
        else:
            self.status_bar.config(text="Zoom Scroll Lock: OFF (Mouse wheel zooms)")

        return is_locked

    def cycle_level(self, event=None):
        """Cycle through the available map levels"""
        if not self.map_renderer.current_zone_map:
            return

        # Get the available levels
        levels = list(self.map_renderer.current_zone_map.levels.keys())
        if not levels:
            return

        # Find the index of the current level
        try:
            current_index = levels.index(self.map_renderer.current_level)
        except ValueError:
            current_index = -1

        # Move to the next level
        next_index = (current_index + 1) % len(levels)
        self.map_renderer.set_level(levels[next_index])

        # Update the status bar
        self.status_bar.config(text=f"Level: {levels[next_index]}")

        # Update the map
        self.update_map()

    def on_mob_filter_changed(self, *args):
        """Handle changes to the mob filter"""
        self.mob_filter = self.mob_filter_var.get().lower()
        self.update_map()

        # Update status bar with filter info
        if self.mob_filter:
            self.status_bar.config(text=f"Filtering mobs: '{self.mob_filter}'")
        else:
            self.status_bar.config(text="Press 'M' to toggle mob levels, 'Z' to toggle zone map, 'C' to toggle corpses, 'S' for zoom scroll lock, 'L' to cycle levels, 'F' to focus filter, '+'/'-' to zoom")

    def clear_mob_filter(self):
        """Clear the mob filter"""
        self.mob_filter_var.set("")

    def handle_keyboard_zoom_in(self, event=None):
        """Handle keyboard zoom in (+ key)"""
        # Zoom in at the center of the canvas
        center_x = self.canvas.winfo_width() / 2
        center_y = self.canvas.winfo_height() / 2
        self.zoom_at_point(center_x, center_y, 1.2)

    def handle_keyboard_zoom_out(self, event=None):
        """Handle keyboard zoom out (- key)"""
        # Zoom out at the center of the canvas
        center_x = self.canvas.winfo_width() / 2
        center_y = self.canvas.winfo_height() / 2
        self.zoom_at_point(center_x, center_y, 1.0 / 1.2)

    def focus_filter(self, event=None):
        """Set focus to the filter entry"""
        for child in self.winfo_children():
            if isinstance(child, ttk.Frame):
                for subchild in child.winfo_children():
                    if isinstance(subchild, ttk.Frame):
                        for widget in subchild.winfo_children():
                            if isinstance(widget, ttk.Entry) and widget.cget("textvariable") == str(self.mob_filter_var):
                                widget.focus_set()
                                return

    def on_closing(self):
        """Handle window closing"""
        if self.running:
            self.memory_reader.close()
        self.destroy()

if __name__ == "__main__":
    app = Application()
    app.protocol("WM_DELETE_WINDOW", app.on_closing)
    app.mainloop()
