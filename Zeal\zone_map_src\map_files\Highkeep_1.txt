P -103.1773, -62.4914, 0.0009, 255, 0, 0, 3, to_Highpass_Hold
P -99.1078, 90.8793, 0.0009, 255, 0, 0, 3, to_Highpass_Hold
P -88.0000, 16.0000, 0.0000, 255, 0, 0, 2, Succor
P 338.0000, 29.0000, -13.9989, 255, 210, 0, 2, <PERSON><PERSON><PERSON>(<PERSON><PERSON>)
P 236.6711, -28.7944, 30.0009, 128, 0, 128, 2, <PERSON><PERSON> 223.3842, -56.1884, 26.0009, 128, 0, 128, 2, <PERSON><PERSON><PERSON><PERSON><PERSON>
P 324.0000, 54.0000, 26.0009, 0, 128, 0, 2, <PERSON><PERSON>_<PERSON><PERSON><PERSON>_(<PERSON><PERSON>)
P 260.0000, -46.0000, 26.0009, 0, 128, 0, 2, <PERSON>_<PERSON><PERSON>_(Baking)
P 221.0000, -62.0000, 0.0009, 0, 128, 0, 2, <PERSON>_(Spells)
P 223.0000, -65.0000, 0.0009, 0, 128, 0, 2, Tarn_(Spells)
P 187.0000, 77.0000, 0.0009, 0, 128, 0, 2, Clerk_(<PERSON>)
P 364.0000, -3.0000, -13.9989, 0, 0, 0, 2, <PERSON><PERSON>_<PERSON>_<PERSON>
P 375.0000, 70.0000, -13.9989, 0, 128, 0, 2, Treasurer_<PERSON>
P 294.0000, 97.0000, 0.0009, 0, 128, 0, 2, <PERSON><PERSON>_(<PERSON><PERSON>ing)
P 357.0000, 42.0000, 0.0009, 128, 128, 128, 2, <PERSON>n_<PERSON><PERSON>ner_(<PERSON>_<PERSON>)
P 250.8351, 103.1589, 3.7961, 0, 0, 255, 1, GS:_<PERSON>_Linens
P 276.8706, -45.5665, -13.9979, 0, 127, 0, 2, Miner_Harton_(Smithing)
P 274.8750, -26.8570, -13.9979, 0, 127, 0, 2, Grahm_Embersmith_(Smithing)
P 570.1170, 39.3470, -13.9979, 240, 240, 240, 2, Jail
P 547.7736, 59.4654, -13.9979, 0, 0, 0, 2, Osargen
P 485.7267, -179.7367, -13.9979, 0, 0, 0, 2, Princess_Lenia
P 380.1178, -65.6733, -13.9979, 240, 240, 240, 2, Torture_Chamber
P 326.8977, -82.1278, -13.9979, 0, 0, 0, 2, Flayer_Hopkins
P 309.7960, -89.5616, -13.9979, 0, 0, 0, 2, Dyrna_N`lith
P 329.2502, -141.8720, -14.9979, 0, 0, 0, 2, Purchin_Oddshot
P 311.3689, -143.9986, -14.9979, 0, 0, 0, 2, Rodrick_Marslet
P 40.4746, -5.5321, -5.9979, 0, 0, 0, 2, Lislia_Goldtune
P 331.1090, 72.7854, 26.0019, 240, 240, 240, 2, King`s_Court
P 243.8048, -49.9404, 26.0019, 240, 240, 240, 2, Kitchen
P 314.8820, -65.0088, 29.0019, 0, 0, 0, 2, Storm_Dragonchaser
P 333.4203, -64.0126, 29.0019, 0, 0, 0, 2, Starr_Dreamspinner
P 304.5440, 18.3433, 49.9706, 0, 0, 0, 2, Tearon_Bleanix
P 306.3627, 37.4964, 49.9706, 240, 240, 0, 2, Locked_Door_(Picklock_50?)
P 232.7640, 26.7042, 69.9706, 240, 240, 0, 2, Fake_Wall
P 186.0622, -102.7117, 69.9706, 240, 240, 0, 2, Fake_Wall
P 226.9224, -131.2935, 69.9706, 240, 240, 0, 2, Secret_Door_(click)
P 342.0543, -91.9145, 69.9706, 240, 240, 240, 2, Treasure_Room
P 165.3672, -78.7527, 61.9706, 0, 0, 0, 2, a_lady_in_waiting
P 85.5409, -22.1583, 61.9706, 0, 0, 0, 2, Lady_McCabe
P 59.8997, -27.1345, 61.9706, 240, 240, 240, 2, Porch
P 95.7806, 78.0114, 61.9706, 0, 0, 0, 2, Mistress_Anna
P 220.5559, 92.5252, 69.9706, 240, 240, 0, 2, Fake_Wall
P 342.8472, 81.6079, 69.9706, 240, 240, 240, 2, Treasure_Room
P -26.4711, -33.4644, -5.9979, 0, 127, 0, 2, Merchant_Dominik_(General)
P 14.3748, 76.5806, -5.9979, 0, 0, 0, 2, Lucky_the_Beggar
P 313.2282, -62.8601, 0.0019, 0, 0, 0, 2, Aeris_Greymalkyn
P 307.0850, -15.5236, 49.9706, 0, 0, 0, 2, Lozani
P 339.8861, -36.1958, 26.0019, 0, 0, 0, 2, Isabella_Cellus
P 304.6392, 55.7805, 26.0019, 0, 0, 0, 2, Isabella_Cellus
P 329.4163, 101.7973, 26.0019, 0, 127, 0, 2, Dealer_Maeline_(Brewing)
P 330.2674, 43.3750, -13.9979, 0, 0, 0, 2, Assistant_Kiolna
P 339.7807, 49.6231, -13.9979, 240, 240, 240, 2, High_Keep_Bank
P 459.3920, -154.1249, -12.9979, 240, 240, 240, 2, High_Keep_Prison
P 100.7465, -36.7531, -43.9667, 240, 240, 240, 2, The_High_Keep_Mines
