//------------------------------------------------------------------------------
// File: DShowASF.idl
//
// Desc: 
//
// Copyright (c) 1992-2000, Microsoft Corporation. All rights reserved.
//------------------------------------------------------------------------------


import	"unknwn.idl";
import	"objidl.idl";
import	"strmif.idl";			// for media type and time definitions
import  "wmsdkidl.idl";

cpp_quote( "EXTERN_GUID( IID_IConfigAsfWriter,0x45086030,0xF7E4,0x486a,0xB5,0x04,0x82,0x6B,0xB5,0x79,0x2A,0x3B );" )

interface IConfigAsfWriter;
interface IWMProfile;

// Interface to control the ASF writer
[
object,
uuid(45086030-F7E4-486a-B504-826BB5792A3B),
pointer_default(unique)
]
interface IConfigAsfWriter : IUnknown
{
    //
    // The user is expected to enumerate profiles using the wmsdk IWMProfileManager
    // method and then pass the desired profile index to the ASF Writer filter via this
    // method. The filter will then try to configure itself for the selected profile.
    //
    // NOTE: These 2 XXXProfileId methods are now obsolete because they assume 
    //       version 4.0 WMSDK profiles. To configure the filter for later profile 
    //       versions using a profile index, use the XXXProfile methods which take 
    //       the IWMProfile* directly.
    //
    HRESULT ConfigureFilterUsingProfileId([in] DWORD dwProfileId);
    HRESULT GetCurrentProfileId([out] DWORD *pdwProfileId);

    //    
    // configure using a pre-defined wmsdk profile guid
    //
    HRESULT ConfigureFilterUsingProfileGuid([in] REFGUID guidProfile);
    HRESULT GetCurrentProfileGuid([out] GUID *pProfileGuid);

    //
    // Use these methods when a custom profile setup is preferred
    //
    HRESULT ConfigureFilterUsingProfile([in] IWMProfile * pProfile);
    HRESULT GetCurrentProfile([out] IWMProfile **ppProfile);

    //
    // allow app to control whether or not to index file
    //
    HRESULT SetIndexMode( [in]  BOOL bIndexFile );
    HRESULT GetIndexMode( [out] BOOL *pbIndexFile );
}

