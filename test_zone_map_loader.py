#!/usr/bin/env python3
"""
Test script for the Zone Map Loader
-----------------------------------
This script tests the zone map loading functionality to ensure it works correctly.
"""

import os
import sys
import tkinter as tk
from tkinter import ttk
from zone_map_loader import ZoneMapLoader

class TestMapViewer(tk.Tk):
    """Simple viewer for testing zone maps"""
    
    def __init__(self, zone_name):
        super().__init__()
        
        self.title(f"Zone Map Test: {zone_name}")
        self.geometry("800x600")
        
        # Create main frame
        main_frame = ttk.Frame(self)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Create controls frame
        controls_frame = ttk.Frame(main_frame)
        controls_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Zone name label
        self.zone_name_var = tk.StringVar(value=zone_name)
        ttk.Label(controls_frame, text="Zone:").pack(side=tk.LEFT, padx=(0, 5))
        ttk.Label(controls_frame, textvariable=self.zone_name_var).pack(side=tk.LEFT, padx=(0, 20))
        
        # Level selector
        self.level_var = tk.IntVar(value=0)
        ttk.Label(controls_frame, text="Level:").pack(side=tk.LEFT, padx=(0, 5))
        self.level_combo = ttk.Combobox(controls_frame, textvariable=self.level_var, width=5)
        self.level_combo.pack(side=tk.LEFT, padx=(0, 20))
        self.level_combo.bind("<<ComboboxSelected>>", self.on_level_changed)
        
        # Zoom controls
        ttk.Button(controls_frame, text="Zoom In (+)", command=self.zoom_in).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(controls_frame, text="Zoom Out (-)", command=self.zoom_out).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(controls_frame, text="Reset View", command=self.reset_view).pack(side=tk.LEFT, padx=(0, 5))
        
        # Canvas for map
        self.canvas = tk.Canvas(main_frame, bg="black")
        self.canvas.pack(fill=tk.BOTH, expand=True)
        
        # Status bar
        self.status_bar = ttk.Label(main_frame, text="")
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X, pady=(5, 0))
        
        # Set up canvas drag for panning
        self.canvas.bind("<ButtonPress-1>", self.start_pan)
        self.canvas.bind("<B1-Motion>", self.pan)
        self.canvas.bind("<MouseWheel>", self.zoom_wheel)
        
        # Initialize map loader and load the specified zone
        self.map_loader = ZoneMapLoader()
        self.zone_map = self.map_loader.load_zone_map(zone_name)
        
        if self.zone_map:
            # Update level selector with available levels
            self.level_combo['values'] = sorted(list(self.zone_map.levels.keys()))
            
            # Set up initial view
            self.scale = 1.0
            self.offset_x = self.canvas.winfo_width() / 2
            self.offset_y = self.canvas.winfo_height() / 2
            self.auto_adjust_view()
            
            # Draw the map
            self.draw_map()
            
            # Update status bar
            self.status_bar.config(text=f"Map loaded successfully. {len(self.zone_map.lines)} lines, {len(self.zone_map.labels)} labels.")
        else:
            self.status_bar.config(text=f"Failed to load map for zone: {zone_name}")
    
    def start_pan(self, event):
        """Start panning the map"""
        self.canvas.scan_mark(event.x, event.y)
        
    def pan(self, event):
        """Pan the map"""
        self.canvas.scan_dragto(event.x, event.y, gain=1)
        
    def zoom_wheel(self, event):
        """Zoom with mouse wheel"""
        if event.delta > 0:
            self.zoom_in()
        else:
            self.zoom_out()
            
    def zoom_in(self):
        """Zoom in on the map"""
        self.scale *= 1.2
        self.draw_map()
        
    def zoom_out(self):
        """Zoom out on the map"""
        self.scale /= 1.2
        self.draw_map()
        
    def reset_view(self):
        """Reset the map view"""
        self.auto_adjust_view()
        self.draw_map()
        
    def auto_adjust_view(self):
        """Automatically adjust the view to fit the zone map"""
        if not self.zone_map:
            return
            
        # Calculate the scale to fit the map in the canvas
        map_width = self.zone_map.max_y - self.zone_map.min_y
        map_height = self.zone_map.max_x - self.zone_map.min_x
        
        if map_width <= 0 or map_height <= 0:
            return
            
        # Calculate scale to fit the map in the canvas with some padding
        scale_x = (self.canvas.winfo_width() * 0.9) / map_width
        scale_y = (self.canvas.winfo_height() * 0.9) / map_height
        self.scale = min(scale_x, scale_y)
        
        # Center the map in the canvas
        center_x = (self.zone_map.max_y + self.zone_map.min_y) / 2
        center_y = (self.zone_map.max_x + self.zone_map.min_x) / 2
        self.offset_x = self.canvas.winfo_width() / 2 + center_x * self.scale
        self.offset_y = self.canvas.winfo_height() / 2 + center_y * self.scale
    
    def world_to_screen(self, x, y):
        """Convert world coordinates to screen coordinates"""
        # In EverQuest, Y and X are swapped and Y is negated for display
        screen_x = -y * self.scale + self.offset_x
        screen_y = -x * self.scale + self.offset_y
        return screen_x, screen_y
    
    def on_level_changed(self, event):
        """Handle level selection change"""
        self.draw_map()
    
    def draw_map(self):
        """Draw the zone map on the canvas"""
        if not self.zone_map:
            return
            
        # Clear the canvas
        self.canvas.delete("all")
        
        # Get the current level
        current_level = self.level_var.get()
        
        # Draw map lines for the current level
        for line in self.zone_map.lines:
            # Skip lines that are not on the current level
            if current_level != 0 and line.level_id != current_level:
                continue
                
            x0, y0 = self.world_to_screen(line.x0, line.y0)
            x1, y1 = self.world_to_screen(line.x1, line.y1)
            
            # Convert RGB to hex color
            color = f"#{line.red:02x}{line.green:02x}{line.blue:02x}"
            
            self.canvas.create_line(
                x0, y0, x1, y1, 
                fill=color, 
                width=1, 
                tags="map_line"
            )
        
        # Draw map labels
        for label in self.zone_map.labels:
            x, y = self.world_to_screen(label.x, label.y)
            
            # Convert RGB to hex color
            color = f"#{label.red:02x}{label.green:02x}{label.blue:02x}"
            
            self.canvas.create_text(
                x, y, 
                text=label.text, 
                fill=color, 
                font=("Arial", 8), 
                tags="map_label"
            )

def main():
    """Main test function"""
    if len(sys.argv) < 2:
        print("Usage: python test_zone_map_loader.py <zone_name>")
        print("Example: python test_zone_map_loader.py qeynos")
        return
    
    zone_name = sys.argv[1].lower()
    app = TestMapViewer(zone_name)
    app.mainloop()

if __name__ == "__main__":
    main()
