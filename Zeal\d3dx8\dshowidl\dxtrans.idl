/*****************************************************************************
* DXTrans.idl *
*-------------*
*
*   Description:
*       This is the IDL file for DirectX installable transform coclass,
*   interface, and type definitions.
*
*   History:
*   09/02/1999  a-matcal    Added IDXTFilterBehavior, IDXTFilter interfaces.
*                           Added CDXTFilterFactory, CDXTFilterBehavior,
*                                 CDXTFilter coclasses.
*
*-----------------------------------------------------------------------------
*   Creation: EWC 07/07/97
*   Copyright Microsoft Corporation 1997-1999
*   All rights reserved.
*
*****************************************************************************/
cpp_quote("#include <servprov.h>")
cpp_quote("#include <ddraw.h>")
cpp_quote("#include <d3d.h>")
cpp_quote("#include <d3drm.h>")
cpp_quote("#include <urlmon.h>")

//--- Import base idl
import "oaidl.idl";
import "ocidl.idl";
import "servprov.idl";
import "comcat.idl";

//
//  Since direct draw does not have an IDL, we'll define DDSURFACEDESC to
//  be a void, but include ddraw.h in the header file.  This makes MIDL happy.
//
#ifndef DDSURFACEDESC
cpp_quote("#if 0")
cpp_quote("// Bogus definition used to make MIDL compiler happy")
typedef void DDSURFACEDESC;
typedef void D3DRMBOX;
typedef void D3DVECTOR;
typedef void D3DRMMATRIX4D;
typedef void* LPSECURITY_ATTRIBUTES;
cpp_quote("#endif")
#endif
//--- Additional includes

//--- Export
cpp_quote( "#ifdef _DXTRANSIMPL")
cpp_quote( "    #define _DXTRANS_IMPL_EXT _declspec(dllexport)")
cpp_quote( "#else")
cpp_quote( "    #define _DXTRANS_IMPL_EXT _declspec(dllimport)")
cpp_quote( "#endif")

//=== Forward References ====================================================
interface IDXTransformFactory;
interface IDXTransform;
interface IDXTaskManager;
interface IDXSurfaceFactory;
interface IDXSurface;
interface IDXARGBSurfaceInit;
interface IDXRawSurface;
interface IDXARGBReadPtr;
interface IDXARGBReadWritePtr;
interface IDXDCLock;
interface IDXTScale;
interface IDXLookupTable;
interface IDXTBindHost;
interface IBindHost;
interface IDXTScaleOutput;
interface IDXGradient;

//=== Constants =============================================================

cpp_quote("//")
cpp_quote("//   All GUIDs for DXTransform are declared in DXTGUID.C in the SDK include directory")
cpp_quote("//")
cpp_quote("EXTERN_C const GUID DDPF_RGB1;") 
cpp_quote("EXTERN_C const GUID DDPF_RGB2;") 
cpp_quote("EXTERN_C const GUID DDPF_RGB4;") 
cpp_quote("EXTERN_C const GUID DDPF_RGB8;") 
cpp_quote("EXTERN_C const GUID DDPF_RGB332;") 
cpp_quote("EXTERN_C const GUID DDPF_ARGB4444;") 
cpp_quote("EXTERN_C const GUID DDPF_RGB565;") 
cpp_quote("EXTERN_C const GUID DDPF_BGR565;") 
cpp_quote("EXTERN_C const GUID DDPF_RGB555;") 
cpp_quote("EXTERN_C const GUID DDPF_ARGB1555;") 
cpp_quote("EXTERN_C const GUID DDPF_RGB24;") 
cpp_quote("EXTERN_C const GUID DDPF_BGR24;") 
cpp_quote("EXTERN_C const GUID DDPF_RGB32;") 
cpp_quote("EXTERN_C const GUID DDPF_BGR32;") 
cpp_quote("EXTERN_C const GUID DDPF_ABGR32;") 
cpp_quote("EXTERN_C const GUID DDPF_ARGB32;") 
cpp_quote("EXTERN_C const GUID DDPF_PMARGB32;") 
cpp_quote("EXTERN_C const GUID DDPF_A1;") 
cpp_quote("EXTERN_C const GUID DDPF_A2;") 
cpp_quote("EXTERN_C const GUID DDPF_A4;") 
cpp_quote("EXTERN_C const GUID DDPF_A8;") 
cpp_quote("EXTERN_C const GUID DDPF_Z8;") 
cpp_quote("EXTERN_C const GUID DDPF_Z16;") 
cpp_quote("EXTERN_C const GUID DDPF_Z24;") 
cpp_quote("EXTERN_C const GUID DDPF_Z32;") 
cpp_quote("//")
cpp_quote("//   Component categories")
cpp_quote("//")
cpp_quote("EXTERN_C const GUID CATID_DXImageTransform;")
cpp_quote("EXTERN_C const GUID CATID_DX3DTransform;")
cpp_quote("EXTERN_C const GUID CATID_DXAuthoringTransform;")
cpp_quote("EXTERN_C const GUID CATID_DXSurface;")
cpp_quote("//")
cpp_quote("//   Service IDs")
cpp_quote("//")
cpp_quote("EXTERN_C const GUID SID_SDirectDraw;")
cpp_quote("EXTERN_C const GUID SID_SDirect3DRM;")
cpp_quote("#define SID_SDXTaskManager CLSID_DXTaskManager")
cpp_quote("#define SID_SDXSurfaceFactory IID_IDXSurfaceFactory")
cpp_quote("#define SID_SDXTransformFactory IID_IDXTransformFactory")

//=== Struct & Enum definitions =============================================

//=== Interface definitions =================================================


[
    uuid(17B59B2B-9CC8-11d1-9053-00C04FD9189D),
    helpstring("IDXBaseObject Interface"),
    pointer_default(unique)
]
interface IDXBaseObject : IUnknown
{
    HRESULT GetGenerationId( [out]ULONG *pID);
    HRESULT IncrementGenerationId([in] BOOL bRefresh);
    HRESULT GetObjectSize( [out] ULONG *pcbSize);
}

//--- Dimension identifiers
typedef enum DXBNDID
{
    DXB_X = 0,
    DXB_Y = 1,
    DXB_Z = 2,
    DXB_T = 3
} DXBNDID;

//--- Bound types
typedef enum DXBNDTYPE
{
    DXBT_DISCRETE,
    DXBT_DISCRETE64,
    DXBT_CONTINUOUS,
    DXBT_CONTINUOUS64
} DXBNDTYPE;

//--- Discrete bounds (image & sound)
typedef struct DXDBND
{
    long  Min;    
    long  Max;
} DXDBND;
typedef DXDBND DXDBNDS[4];

typedef struct DXDBND64
{
    LONGLONG Min;    
    LONGLONG Max;
} DXDBND64;
typedef DXDBND64 DXDBNDS64[4];

//--- Continuous bounds (geometry)
typedef struct DXCBND
{
    float Min;    
    float Max;
} DXCBND;
typedef DXCBND DXCBNDS[4];

typedef struct DXCBND64
{
    double Min;    
    double Max;
} DXCBND64;
typedef DXCBND64 DXCBNDS64[4];

//--- Combined space
typedef union DXBNDS switch( DXBNDTYPE eType ) u
{
  case DXBT_DISCRETE:
    DXDBND D[4];
  case DXBT_DISCRETE64:
    DXDBND64 LD[4];
  case DXBT_CONTINUOUS:
    DXCBND C[4];
  case DXBT_CONTINUOUS64:
    DXCBND64 LC[4];
} DXBNDS;

//--- Discrete 4D vector
typedef long DXDVEC[4];
typedef LONGLONG DXDVEC64[4];

//--- Continous 4D vector
typedef float DXCVEC[4];
typedef double DXCVEC64[4];

//--- Combined space vector
typedef union DXVEC switch( DXBNDTYPE eType ) u
{
  case DXBT_DISCRETE:
    long D[4];
  case DXBT_DISCRETE64:
    LONGLONG LD[4];
  case DXBT_CONTINUOUS:
    float C[4];
  case DXBT_CONTINUOUS64:
    double LC[4];
} DXVEC;

//--- IDXTransformFactory ---------------------------------------------------
[
    uuid(6A950B2B-A971-11d1-81C8-0000F87557DB),
    helpstring("IDXTransformFactory Interface"),
    pointer_default(unique)
]
interface IDXTransformFactory : IServiceProvider
{
    HRESULT SetService( [in]REFGUID guidService, 
                        [in]IUnknown *pUnkService,
                        [in]BOOL bWeakReference);

    HRESULT CreateTransform( [in, size_is(ulNumInputs)]IUnknown** punkInputs,
                             [in]ULONG ulNumInputs,
                             [in, size_is(ulNumOutputs)]IUnknown** punkOutputs,
                             [in]ULONG ulNumOutputs,
                             [in]IPropertyBag* pInitProps, [in]IErrorLog* pErrLog,
                             [in]REFCLSID TransCLSID, [in]REFIID TransIID,
                             [out, iid_is(TransIID)]void** ppTransform );

    HRESULT InitializeTransform( [in]IDXTransform* pTransform,
                                 [in, size_is(ulNumInputs)]IUnknown** punkInputs,
                                 [in]ULONG ulNumInputs,
                                 [in, size_is(ulNumOutputs)]IUnknown** punkOutputs,
                                 [in]ULONG ulNumOutputs,
                                 [in]IPropertyBag* pInitProps, [in]IErrorLog* pErrLog );
};

//--- IDXTransform -------------------------------------------------

typedef enum DXTMISCFLAGS
{
    DXTMF_BLEND_WITH_OUTPUT   = ( 1L << 0 ),
    DXTMF_DITHER_OUTPUT       = ( 1L << 1 ),
    DXTMF_OPTION_MASK         = (0x0000FFFF),   // Low word is settable options
    DXTMF_VALID_OPTIONS       = (DXTMF_BLEND_WITH_OUTPUT | DXTMF_DITHER_OUTPUT),
    //
    //  Status flags can not be changed by call to SetMiscFlags
    //
    DXTMF_BLEND_SUPPORTED     = ( 1L << 16 ),
    DXTMF_DITHER_SUPPORTED    = ( 1L << 17 ),
    DXTMF_INPLACE_OPERATION   = ( 1L << 24 ),
    DXTMF_BOUNDS_SUPPORTED    = ( 1L << 25 ),
    DXTMF_PLACEMENT_SUPPORTED = ( 1L << 26 ),
    DXTMF_QUALITY_SUPPORTED   = ( 1L << 27 ),
    DXTMF_OPAQUE_RESULT       = ( 1L << 28 )
} DXTMISCFLAGS;


typedef enum DXINOUTINFOFLAGS
{
    DXINOUTF_OPTIONAL = ( 1L << 0)
} DXINOUTINFOFLAGS;

[
    uuid(30A5FB78-E11F-11d1-9064-00C04FD9189D),
    helpstring("IDXTransform Interface"),
    pointer_default(unique)
]
interface IDXTransform : IDXBaseObject
{
    HRESULT Setup( [in, size_is(ulNumInputs)] IUnknown * const * punkInputs,
                   [in]ULONG ulNumInputs,
                   [in, size_is(ulNumOutputs)] IUnknown * const * punkOutputs,
                   [in]ULONG ulNumOutputs,
                   [in]DWORD dwFlags );
    HRESULT Execute( [in]const GUID* pRequestID, [in]const DXBNDS *pClipBnds,
                     [in]const DXVEC *pPlacement );
    HRESULT MapBoundsIn2Out( [in] const DXBNDS *pInBounds,
                             [in]ULONG ulNumInBnds,
                             [in]ULONG ulOutIndex,
                             [out]DXBNDS *pOutBounds );
    HRESULT MapBoundsOut2In( [in] ULONG ulOutIndex,
                             [in] const DXBNDS *pOutBounds,
                             [in] ULONG ulInIndex,
                             [out]DXBNDS *pInBounds );
    HRESULT SetMiscFlags( [in] DWORD dwMiscFlags);
    HRESULT GetMiscFlags( [out]DWORD * pdwMiscFlags );
    HRESULT GetInOutInfo( [in]BOOL bIsOutput, [in]ULONG ulIndex,
                          [out]DWORD *pdwFlags, [out, size_is(*pcIDs)] GUID *pIDs,
                          [in, out] ULONG *pcIDs,
                          [out] IUnknown **ppUnkCurrentObject);
    HRESULT SetQuality( [in] float fQuality );
    HRESULT GetQuality( [out] float * fQuality );
};


[
    uuid(30A5FB79-E11F-11d1-9064-00C04FD9189D),
    helpstring("IDXSurfacePick Interface"),
    pointer_default(unique)
]
interface IDXSurfacePick : IUnknown
{
    HRESULT PointPick([in]const DXVEC *pPoint,
                      [out]ULONG * pulInputSurfaceIndex,
                      [out]DXVEC *pInputPoint);
}


//--- IDXTBindHost ---------------------------------------------------
//  This interface is used to set a site-specific bind host for a transform.
//  Only transforms that need access to a bind host need to implement this interface.

//
//  For some reason, MIDL does not like IBindHost, so we've declared this interface local
//
[
    uuid(D26BCE55-E9DC-11d1-9066-00C04FD9189D),
    helpstring("IDXTBindHost Interface"),
    pointer_default(unique),
    local
]
interface IDXTBindHost : IUnknown
{
    HRESULT SetBindHost([in] IBindHost * pBindHost);
}

//--- IDXTaskManager ---------------------------------------------------
// This interface is used to implement a task managment service provider
// to optimize thread usage.

// Function type prototypes
typedef void (__stdcall DXTASKPROC)(void *pTaskData, BOOL* pbContinueProcessing );
typedef DXTASKPROC *PFNDXTASKPROC;

typedef void (__stdcall DXAPCPROC)(DWORD dwData);
typedef DXAPCPROC *PFNDXAPCPROC;

cpp_quote("#ifdef __cplusplus")
cpp_quote("typedef struct DXTMTASKINFO" )
cpp_quote("{")
cpp_quote("    PFNDXTASKPROC pfnTaskProc;       // Pointer to function to execute")
cpp_quote("    PVOID         pTaskData;         // Pointer to argument data")
cpp_quote("    PFNDXAPCPROC  pfnCompletionAPC;  // Pointer to completion APC proc")
cpp_quote("    DWORD         dwCompletionData;  // Pointer to APC proc data")
cpp_quote("    const GUID*   pRequestID;        // Used to identify groups of tasks")
cpp_quote("} DXTMTASKINFO;")
cpp_quote("#else")
    typedef struct DXTMTASKINFO
    {
        PVOID       pfnTaskProc;       // Pointer to function to execute
        PVOID       pTaskData;         // Pointer to argument data
        PVOID       pfnCompletionAPC;  // Pointer to completion APC proc
        DWORD       dwCompletionData;  // Pointer to APC proc data
        const GUID* pRequestID;        // Used to identify groups of tasks
    } DXTMTASKINFO;
cpp_quote("#endif")

[
    local,
    uuid(254DBBC1-F922-11d0-883A-3C8B00C10000),
    helpstring("IDXTaskManager Interface"),
    pointer_default(unique)
]
interface IDXTaskManager : IUnknown
{
    HRESULT QueryNumProcessors( [out]ULONG* pulNumProc );
    HRESULT SetThreadPoolSize( [in]ULONG ulNumThreads );
    HRESULT GetThreadPoolSize( [out]ULONG* pulNumThreads );
    HRESULT SetConcurrencyLimit( [in]ULONG ulNumThreads );
    HRESULT GetConcurrencyLimit( [out]ULONG* pulNumThreads );
    HRESULT ScheduleTasks( [in]DXTMTASKINFO TaskInfo[],
                           [in]HANDLE Events[],
                           [out]DWORD TaskIDs[],
                           [in]ULONG ulNumTasks, [in]ULONG ulWaitPeriod );
    HRESULT TerminateTasks( [in]DWORD TaskIDs[], [in]ULONG ulCount,
                            [in]ULONG ulTimeOut );
    HRESULT TerminateRequest( [in]REFIID RequestID, [in]ULONG ulTimeOut );
};

//--- IDXSurfaceFactory ---------------------------------------------------
//
//  We want an operator so that we can cast from a DXSAMPLE to a DWORD, so for C++ we will
//  define the structure a special way.
//

cpp_quote("#ifdef __cplusplus")
cpp_quote("/////////////////////////////////////////////////////")
cpp_quote("")
cpp_quote("class DXBASESAMPLE;")
cpp_quote("class DXSAMPLE;")
cpp_quote("class DXPMSAMPLE;")
cpp_quote("")
cpp_quote("/////////////////////////////////////////////////////")
cpp_quote("")
cpp_quote("class DXBASESAMPLE")
cpp_quote("{")
cpp_quote("public:")
cpp_quote("    BYTE Blue;")
cpp_quote("    BYTE Green;")
cpp_quote("    BYTE Red;")
cpp_quote("    BYTE Alpha;")
cpp_quote("    DXBASESAMPLE() {}")
cpp_quote("    DXBASESAMPLE(const BYTE alpha, const BYTE red, const BYTE green, const BYTE blue) :")
cpp_quote("        Alpha(alpha),")
cpp_quote("        Red(red),")
cpp_quote("        Green(green),")
cpp_quote("        Blue(blue) {}")
cpp_quote("    DXBASESAMPLE(const DWORD val) { *this = (*(DXBASESAMPLE *)&val); }")
cpp_quote("    operator DWORD () const {return *((DWORD *)this); }")
cpp_quote("    DWORD operator=(const DWORD val) { return *this = *((DXBASESAMPLE *)&val); }")
cpp_quote("}; // DXBASESAMPLE")
cpp_quote("")
cpp_quote("/////////////////////////////////////////////////////")
cpp_quote("")
cpp_quote("class DXSAMPLE : public DXBASESAMPLE")
cpp_quote("{")
cpp_quote("public:")
cpp_quote("    DXSAMPLE() {}")
cpp_quote("    DXSAMPLE(const BYTE alpha, const BYTE red, const BYTE green, const BYTE blue) :")
cpp_quote("         DXBASESAMPLE(alpha, red, green, blue) {}")
cpp_quote("    DXSAMPLE(const DWORD val) { *this = (*(DXSAMPLE *)&val); }")
cpp_quote("    operator DWORD () const {return *((DWORD *)this); }")
cpp_quote("    DWORD operator=(const DWORD val) { return *this = *((DXSAMPLE *)&val); }")
cpp_quote("    operator DXPMSAMPLE() const;")
cpp_quote("}; // DXSAMPLE")
cpp_quote("")
cpp_quote("/////////////////////////////////////////////////////")
cpp_quote("")
cpp_quote("class DXPMSAMPLE : public DXBASESAMPLE")
cpp_quote("{")
cpp_quote("public:")
cpp_quote("    DXPMSAMPLE() {}")
cpp_quote("    DXPMSAMPLE(const BYTE alpha, const BYTE red, const BYTE green, const BYTE blue) :")
cpp_quote("         DXBASESAMPLE(alpha, red, green, blue) {}")
cpp_quote("    DXPMSAMPLE(const DWORD val) { *this = (*(DXPMSAMPLE *)&val); }")
cpp_quote("    operator DWORD () const {return *((DWORD *)this); }")
cpp_quote("    DWORD operator=(const DWORD val) { return *this = *((DXPMSAMPLE *)&val); }")
cpp_quote("    operator DXSAMPLE() const;")
cpp_quote("}; // DXPMSAMPLE")
cpp_quote("")
cpp_quote("//")
cpp_quote("// The following cast operators are to prevent a direct assignment of a DXSAMPLE to a DXPMSAMPLE")
cpp_quote("//")
cpp_quote("inline DXSAMPLE::operator DXPMSAMPLE() const { return *((DXPMSAMPLE *)this); }")
cpp_quote("inline DXPMSAMPLE::operator DXSAMPLE() const { return *((DXSAMPLE *)this); }")

cpp_quote("#else // !__cplusplus")
typedef struct DXBASESAMPLE
{
    BYTE Blue;
    BYTE Green;
    BYTE Red; 
    BYTE Alpha;
} DXBASESAMPLE;

typedef struct DXSAMPLE
{
    BYTE Blue;
    BYTE Green;
    BYTE Red; 
    BYTE Alpha;
} DXSAMPLE;

typedef struct DXPMSAMPLE
{
    BYTE Blue;
    BYTE Green;
    BYTE Red; 
    BYTE Alpha;
} DXPMSAMPLE;
cpp_quote("#endif // !__cplusplus")

typedef enum DXRUNTYPE
{
    DXRUNTYPE_CLEAR  = 0,        // The run is zero Alpha
    DXRUNTYPE_OPAQUE = 1,        // The run is full Alpha (i.e. 255)
    DXRUNTYPE_TRANS  = 2,        // The run is non-zero Alpha
    DXRUNTYPE_UNKNOWN= 3         // The run type is unknown.  Caller must inspect.
} DXRUNTYPE;

const ULONG DX_MAX_RUN_INFO_COUNT = 128; // Defines the maximum number of RUNINFOs in a single row


cpp_quote("// Ignore the definition used by MIDL for TLB generation")
cpp_quote("#if 0")

typedef struct DXRUNINFO
{
    ULONG Bitfields;
} DXRUNINFO;

cpp_quote("#endif // 0")

// Emit the C definition to the H file directly, as bit fields are not
// supported by MIDL.

cpp_quote("typedef struct DXRUNINFO")
cpp_quote("{")
cpp_quote("    ULONG   Type  : 2;   // Type")
cpp_quote("    ULONG   Count : 30;  // Number of samples in run")
cpp_quote("} DXRUNINFO;")


typedef enum DXSFCREATE
{
    DXSF_FORMAT_IS_CLSID    = ( 1L << 0 ),
    DXSF_NO_LAZY_DDRAW_LOCK = ( 1L << 1 )
} DXSFCREATE;

typedef enum DXBLTOPTIONS
{
    DXBOF_DO_OVER          = (1L << 0),
    DXBOF_DITHER           = (1L << 1)
} DXBLTOPTIONS;



[
    uuid(144946F5-C4D4-11d1-81D1-0000F87557DB),
    helpstring("IDXSurfaceFactory Interface"),
    pointer_default(unique)
]
interface IDXSurfaceFactory : IUnknown
{
    [local] HRESULT CreateSurface([in] IUnknown *pDirectDraw,
                          [in] const DDSURFACEDESC * pDDSurfaceDesc,
                          [in] const GUID * pFormatID,
                          [in] const DXBNDS *pBounds, 
                          [in] DWORD dwFlags,
                          [in] IUnknown *punkOuter,
                          [in] REFIID riid, 
                          [out, iid_is( riid )] void ** ppDXSurface);

    HRESULT CreateFromDDSurface([in] IUnknown *pDDrawSurface,
                          [in] const GUID *pFormatID,
                          [in] DWORD dwFlags,
                          [in] IUnknown *punkOuter,
                          [in] REFIID riid, 
                          [out, iid_is( riid )] void ** ppDXSurface);

    [local] HRESULT LoadImage(
                      [in] const LPWSTR pszFileName,
                      [in] IUnknown *pDirectDraw,
                      [in] const DDSURFACEDESC * pDDSurfaceDesc,
                      [in] const GUID *pFormatID,
                      [in] REFIID riid, 
                      [out, iid_is( riid )] void ** ppDXSurface);

    [local] HRESULT LoadImageFromStream([in] IStream *pStream,
                                [in] IUnknown *pDirectDraw,
                                [in] const DDSURFACEDESC * pDDSurfaceDesc,
                                [in] const GUID *pFormatID,
                                [in] REFIID riid, 
                                [out, iid_is( riid )] void ** ppDXSurface);

    [local] HRESULT CopySurfaceToNewFormat( [in]IDXSurface* pSrc,
                                            [in] IUnknown *pDirectDraw,
                                            [in] const DDSURFACEDESC * pDDSurfaceDesc,
                                            [in] const GUID *pDestFormatID,
                                            [out] IDXSurface** ppNewSurface );

    [local] HRESULT CreateD3DRMTexture([in] IDXSurface *pSrc,
                                       [in] IUnknown *pDirectDraw,
                                       [in] IUnknown *pD3DRM3,
                                       [in] REFIID riid,
                                       [out, iid_is(riid)] void **ppTexture3);

    HRESULT BitBlt([in] IDXSurface *pDest,
                   [in] const DXVEC *pPlacement,
                   [in] IDXSurface *pSrc,
                   [in] const DXBNDS *pClipBounds,
                   [in] DWORD dwFlags);
}


typedef enum DXSURFMODCOMPOP
{
    DXSURFMOD_COMP_OVER         = 0,
    DXSURFMOD_COMP_ALPHA_MASK   = 1,
    DXSURFMOD_COMP_MAX_VALID    = 1
} DXSURFMODCOMPOP;

    
[
    uuid(9EA3B637-C37D-11d1-905E-00C04FD9189D),
    helpstring("IDXSurfaceModifier Interface"),
    pointer_default(unique)
]
interface IDXSurfaceModifier : IUnknown
{
    HRESULT SetFillColor([in] DXSAMPLE Color);
    HRESULT GetFillColor([out] DXSAMPLE *pColor);
    HRESULT SetBounds([in] const DXBNDS *pBounds ); // Get supported though IDXSurface interface
    HRESULT SetBackground([in] IDXSurface *pSurface);
    HRESULT GetBackground([out] IDXSurface **ppSurface);
    HRESULT SetCompositeOperation([in] DXSURFMODCOMPOP CompOp);
    HRESULT GetCompositeOperation([out] DXSURFMODCOMPOP *pCompOp);
    //
    //  The following methods only apply to the FOREGROUND surface
    //
    HRESULT SetForeground([in] IDXSurface *pSurface, [in] BOOL bTile, [in] const POINT * pOrigin);
    HRESULT GetForeground([out] IDXSurface **ppSurface, [out] BOOL *pbTile, [out] POINT * pOrigin);
    HRESULT SetOpacity([in] float Opacity);
    HRESULT GetOpacity([out] float *pOpacity);    
    HRESULT SetLookup( [in]IDXLookupTable * pLookupTable );
    HRESULT GetLookup( [out]IDXLookupTable ** ppLookupTable );
}



//--- IDXSurface ---------------------------------------------------
typedef enum DXSAMPLEFORMATENUM
{
    DXPF_FLAGSMASK      = (0xFFFF0000), // Top word is flags, low word is enum  
    DXPF_NONPREMULT     = (0x00010000), // Flags to be OR'd with pixel formats
    DXPF_TRANSPARENCY   = (0x00020000), // Color key or one-bit alpha (alpha only 0 or 0xFF)
    DXPF_TRANSLUCENCY   = (0x00040000), // Alpha can be any value from 0->0xFF
    //
    //  This 3-bit field is used to determine what type of dithering to be used
    //  
    DXPF_2BITERROR      = (0x00200000), // 2 bits of error term 
    DXPF_3BITERROR      = (0x00300000), // 3 bits of error term for color (16-bit color)
    DXPF_4BITERROR      = (0x00400000), // 4 bits of error term (ARGB 4444)
    DXPF_5BITERROR      = (0x00500000), // 5 bits of error term for color (8-bit color)
    DXPF_ERRORMASK      = (0x00700000), // Mask of bits used for dithering

    DXPF_NONSTANDARD    = (0),  // To be used for any surface that is not one of the following formats
                                // This can be combined with DXPFNONPREMULT if the surface can work
                                // better in non-premultiplied space.
    DXPF_PMARGB32       = (1 | DXPF_TRANSPARENCY | DXPF_TRANSLUCENCY),
    DXPF_ARGB32         = (2 | DXPF_NONPREMULT | DXPF_TRANSPARENCY | DXPF_TRANSLUCENCY),
    DXPF_ARGB4444       = (3 | DXPF_NONPREMULT | DXPF_TRANSPARENCY | DXPF_TRANSLUCENCY | DXPF_4BITERROR),
    DXPF_A8             = (4 | DXPF_TRANSPARENCY | DXPF_TRANSLUCENCY),
    DXPF_RGB32          = (5),
    DXPF_RGB24          = (6),
    DXPF_RGB565         = (7 | DXPF_3BITERROR),
    DXPF_RGB555         = (8 | DXPF_3BITERROR),
    DXPF_RGB8           = (9 | DXPF_5BITERROR),
    DXPF_ARGB1555       = (10 | DXPF_TRANSPARENCY | DXPF_3BITERROR),
    DXPF_RGB32_CK       = (DXPF_RGB32 | DXPF_TRANSPARENCY),
    DXPF_RGB24_CK       = (DXPF_RGB24 | DXPF_TRANSPARENCY),
    DXPF_RGB555_CK      = (DXPF_RGB555 | DXPF_TRANSPARENCY),
    DXPF_RGB565_CK      = (DXPF_RGB565 | DXPF_TRANSPARENCY),
    DXPF_RGB8_CK        = (DXPF_RGB8 | DXPF_TRANSPARENCY)
} DXSAMPLEFORMATENUM;



typedef enum DXLOCKSURF
{
    DXLOCKF_READ        =       0,
    DXLOCKF_READWRITE   =       (1 << 0),
    DXLOCKF_EXISTINGINFOONLY =  (1 << 1),   // If used in conjunction with WANTRUNINFO will prevent creation of a runmap if one does not exist
    DXLOCKF_WANTRUNINFO =       (1 << 2),
    //
    // The flags in the high word should be specific to the type of pointer that
    // is requested.  These flags define ARGB flags.  These flags are advisory and
    // are not required to be set for ARGB locks.
    //
    DXLOCKF_NONPREMULT  =   (1 << 16),    // Caller will request non-premultiplied data
    DXLOCKF_VALIDFLAGS  =   (DXLOCKF_READWRITE | DXLOCKF_EXISTINGINFOONLY | DXLOCKF_WANTRUNINFO | DXLOCKF_NONPREMULT)
} DXLOCKSURF;

typedef enum DXSURFSTATUS
{
    DXSURF_TRANSIENT    =   (1 << 0),   // Data in this surface changes often.    
    DXSURF_READONLY     =   (1 << 1),   // Surface is read-only
    DXSURF_VALIDFLAGS   =   (DXSURF_TRANSIENT | DXSURF_READONLY)    
} DXSURFSTATUS;

[
    uuid(B39FD73F-E139-11d1-9065-00C04FD9189D),
    helpstring("IDXSurface Interface"),
    pointer_default(unique)
]
interface IDXSurface : IDXBaseObject
{
    HRESULT GetPixelFormat([out] GUID * pFormatID, [out] DXSAMPLEFORMATENUM *pSampleFormatEnum); 
    HRESULT GetBounds( [out]DXBNDS *pBounds );
    HRESULT GetStatusFlags([out] DWORD * pdwStatusFlags);
    HRESULT SetStatusFlags([in] DWORD dwStatusFlags);
    HRESULT LockSurface( [in]const DXBNDS *pBounds, [in]ULONG ulTimeOut, [in]DWORD dwFlags, 
                         [in]REFIID riid, [out, iid_is(riid)] void **ppPointer,
                         [out]ULONG* pulGenerationId );
    HRESULT GetDirectDrawSurface( [in] REFIID riid,
                                  [out, iid_is(riid)] void ** ppSurface);
    HRESULT GetColorKey(DXSAMPLE * pColorKey);  // Can return E_NOTIMPL
    HRESULT SetColorKey(DXSAMPLE ColorKey);     // Set color of 0 to get rid of color key, can return E_NOTIMPL
    HRESULT LockSurfaceDC( [in] const DXBNDS *pBounds, [in]ULONG ulTimeOut, [in] DWORD dwFlags,
                           [out] IDXDCLock **ppDCLock);
    HRESULT SetAppData(DWORD_PTR dwAppData);
    HRESULT GetAppData(DWORD_PTR *pdwAppData);
}

//--- IDXSurfaceInit ---------------------------------------------------
[
    uuid(9EA3B639-C37D-11d1-905E-00C04FD9189D),
    helpstring("IDXSurfaceInit Interface"),
    pointer_default(unique),
    local
]
interface IDXSurfaceInit : IUnknown
{
    HRESULT InitSurface([in] IUnknown *pDirectDraw,
                        [in] const DDSURFACEDESC *pDDSurfaceDesc,
                        [in] const GUID * pFormatID,
                        [in] const DXBNDS *pBounds,
                        [in] DWORD dwFlags);
};


//--- IDXARGBSurfaceInit ---------------------------------------------------
[
    uuid(9EA3B63A-C37D-11d1-905E-00C04FD9189D),
    helpstring("IDXARGBSurfaceInit Interface"),
    pointer_default(unique),
    local
]
interface IDXARGBSurfaceInit : IDXSurfaceInit
{
    HRESULT InitFromDDSurface( [in] IUnknown *pDDrawSurface,
                               [in] const GUID * pFormatID,
                               [in] DWORD dwFlags);
    HRESULT InitFromRawSurface([in] IDXRawSurface *pRawSurface);
};

//--- IDXARGBReadPtr ---------------------------------------------------
typedef struct tagDXNATIVETYPEINFO
{
    BYTE *          pCurrentData;
    BYTE *          pFirstByte;
    long            lPitch;
    DWORD           dwColorKey;
} DXNATIVETYPEINFO;

typedef struct tagDXPACKEDRECTDESC
{
    DXBASESAMPLE    *pSamples;
    BOOL            bPremult;
    RECT            rect;
    long            lRowPadding;
} DXPACKEDRECTDESC;

typedef struct tagDXOVERSAMPLEDESC
{
    POINT       p;
    DXPMSAMPLE  Color;
} DXOVERSAMPLEDESC;
        

[
    uuid(EAAAC2D6-C290-11d1-905D-00C04FD9189D),
    helpstring("IDXARGBReadPtr Interface"),
    pointer_default(unique),
    local
]
interface IDXARGBReadPtr : IUnknown
{
    HRESULT GetSurface( [in]REFIID riid, [out, iid_is( riid )]void ** ppSurface);
    DXSAMPLEFORMATENUM GetNativeType( [out]DXNATIVETYPEINFO *pInfo );
    void Move( [in]long cSamples );
    void MoveToRow( [in]ULONG y );
    void MoveToXY( [in]ULONG x, [in]ULONG y);
    ULONG MoveAndGetRunInfo( [in]ULONG Row, [out] const DXRUNINFO** ppInfo );  // Returns count of runs
    DXSAMPLE   * Unpack( [in]DXSAMPLE* pSamples, [in]ULONG cSamples, [in]BOOL bMove );
    DXPMSAMPLE * UnpackPremult( [in]DXPMSAMPLE* pSamples, [in]ULONG cSamples, [in]BOOL bMove );
    void UnpackRect([in] const DXPACKEDRECTDESC * pRectDesc);
};


//--- IDXARGBReadWritePtr ---------------------------------------------------
[
    uuid(EAAAC2D7-C290-11d1-905D-00C04FD9189D),
    helpstring("IDXARGBReadWritePtr Interface"),
    pointer_default(unique),
    local
]
interface IDXARGBReadWritePtr : IDXARGBReadPtr
{
    void PackAndMove( [in]const DXSAMPLE *pSamples, [in]ULONG cSamples );
    void PackPremultAndMove( [in]const DXPMSAMPLE *pSamples, [in]ULONG cSamples );
    void PackRect([in]const DXPACKEDRECTDESC *pRectDesc);
    void CopyAndMoveBoth( [in]DXBASESAMPLE *pScratchBuffer, [in]IDXARGBReadPtr *pSrc,
                          [in]ULONG cSamples, [in]BOOL bIsOpaque );
    void CopyRect( [in] DXBASESAMPLE *pScratchBuffer, 
                   [in] const RECT *pDestRect, [in]IDXARGBReadPtr *pSrc,
                   [in] const POINT *pSrcOrigin, [in]BOOL bIsOpaque);
    void FillAndMove( [in]DXBASESAMPLE *pScratchBuffer, [in]DXPMSAMPLE SampVal,
                      [in]ULONG cSamples, [in]BOOL bDoOver );
    void FillRect( [in]const RECT *pRect, [in]DXPMSAMPLE SampVal, [in]BOOL bDoOver );
    void OverSample( [in]const DXOVERSAMPLEDESC * pOverDesc);
    void OverArrayAndMove([in]DXBASESAMPLE *pScratchBuffer, 
                          [in] const DXPMSAMPLE *pSrc,
                          [in] ULONG cSamples);
};


[
    uuid(0F619456-CF39-11d1-905E-00C04FD9189D),
    helpstring("IDXDCLock Interface"),
    pointer_default(unique),
    local
]
interface IDXDCLock : IUnknown
{
    HDC GetDC(void);
}

//
//  Generic interface that any transform can support which allows caller to specify the
//  desired output bounds.
//
[
    uuid(B2024B50-EE77-11d1-9066-00C04FD9189D),
    helpstring("IDXTScaleOutput Interface"),
    pointer_default(unique)
]
interface IDXTScaleOutput : IUnknown
{
    HRESULT SetOutputSize([in] const SIZE OutSize, [in] BOOL bMaintainAspect);
};


//
//  Simple gradient
//
[
    uuid(B2024B51-EE77-11d1-9066-00C04FD9189D),
    helpstring("IDXGradient Interface"),
    pointer_default(unique)
]
interface IDXGradient : IDXTScaleOutput
{
    HRESULT SetGradient(DXSAMPLE StartColor, DXSAMPLE EndColor, BOOL bHorizontal);
    HRESULT GetOutputSize([out] SIZE *pOutSize);
};

//--- IDXTScale --------------------------------------------------------
// This is the control interface for the simple scale transform
//
[
    uuid(B39FD742-E139-11d1-9065-00C04FD9189D),
    helpstring("IDXTScale Interface"),
    pointer_default(unique)
]
interface IDXTScale : IUnknown
{
    HRESULT SetScales( [in]float Scales[2] );
    HRESULT GetScales( [out]float Scales[2] );
    HRESULT ScaleFitToSize( [in,out]DXBNDS* pClipBounds,
                            [in]SIZE FitToSize, [in]BOOL bMaintainAspect );
};



//--- IDXEffect -------------------------------------------------
//  This interface is used to generically control transforms that
//  are transition effects.
typedef enum DISPIDDXEFFECT
{
    DISPID_DXECAPABILITIES = 10000, // Start at 10000 to avoid conflicts with inhereted interfaces
    DISPID_DXEPROGRESS,
    DISPID_DXESTEP,
    DISPID_DXEDURATION,
    DISPID_DXE_NEXT_ID
} DISPIDDXBOUNDEDEFFECT;

typedef enum DXEFFECTTYPE
{
    DXTET_PERIODIC = (1 << 0),  // Result at 1 is same as result at 0
    DXTET_MORPH = (1 << 1)      // Transition between 2 inputs (input 0 to input 1)
} DXEFFECTTYPE;




[
    object,
    uuid(E31FB81B-1335-11d1-8189-0000F87557DB),
    dual,
    helpstring("IDXEffect Interface"),
    pointer_default(unique)
]
interface IDXEffect : IDispatch
{
    [propget, id(DISPID_DXECAPABILITIES)]
        HRESULT Capabilities([out, retval] long *pVal);
    [propget, id(DISPID_DXEPROGRESS)]
        HRESULT Progress([out, retval] float *pVal);
    [propput, id(DISPID_DXEPROGRESS)]
        HRESULT Progress([in] float newVal);
    [propget, id(DISPID_DXESTEP)]
        HRESULT StepResolution([out, retval] float *pVal);
    [propget, id(DISPID_DXEDURATION)]
        HRESULT Duration([out, retval] float *pVal);
    [propput, id(DISPID_DXEDURATION)]
        HRESULT Duration([in] float newVal);
};

//--- IDXLookupTable ---------------------------------------------
[
    uuid(01BAFC7F-9E63-11d1-9053-00C04FD9189D),
    helpstring("IDXLookupTable Interface"),
    pointer_default(unique)
]
interface IDXLookupTable : IDXBaseObject
{
    HRESULT GetTables( [out]BYTE RedLUT[256],
                       [out]BYTE GreenLUT[256],
                       [out]BYTE BlueLUT[256],
                       [out]BYTE AlphaLUT[256] );

    HRESULT IsChannelIdentity([out] DXBASESAMPLE * pSampleBools);
    HRESULT GetIndexValues([in] ULONG Index, [out] DXBASESAMPLE *pSample);
    HRESULT ApplyTables([in, out] DXSAMPLE *pSamples, [in] ULONG cSamples);
};



typedef struct DXRAWSURFACEINFO
{
    BYTE        * pFirstByte;
    long        lPitch;
    ULONG       Width;
    ULONG       Height;
    const GUID  * pPixelFormat;
    HDC         hdc;
    DWORD       dwColorKey;     // Note:  High byte must == 0xFF for color keyed surface.  Low 3 bytes are native data type.
    DXBASESAMPLE * pPalette;
} DXRAWSURFACEINFO;

//
//  User created objects support IDXRawSurface
//

[
    uuid(09756C8A-D96A-11d1-9062-00C04FD9189D),
    helpstring("IDXRawSurface Interface"),
    pointer_default(unique),
    local
]
interface IDXRawSurface : IUnknown
{
    HRESULT GetSurfaceInfo(DXRAWSURFACEINFO * pSurfaceInfo);
}


//+-----------------------------------------------------------------------------
//
//  IHTMLDXTransform
//
//------------------------------------------------------------------------------

[
    uuid(30E2AB7D-4FDD-4159-B7EA-DC722BF4ADE5),
    helpstring("IHTMLDXTransform Interface"),
    pointer_default(unique),
    local
]
interface IHTMLDXTransform : IUnknown
{
    HRESULT SetHostUrl(BSTR bstrHostUrl);
}


//=== CoClass definitions =================================================

[
    uuid(54314D1D-35FE-11d1-81A1-0000F87557DB),
    version(1.0),
    helpstring("Microsoft DirectX Transform 1.0 Type Library")
]
library DXTRANSLib
{
    importlib("stdole32.tlb");
    importlib("stdole2.tlb");

    ///////////////////////////////
    // DXTransformFactory CoClass
    ///////////////////////////////
    [
        uuid(D1FE6762-FC48-11D0-883A-3C8B00C10000),
        helpstring("DXTransformFactory Class")
    ]
    coclass DXTransformFactory
    {
        [default] interface IDXTransformFactory;
        interface IDXSurfaceFactory;
    };

    ///////////////////////////////
    // DXTaskManager CoClass
    ///////////////////////////////
    [
        uuid(4CB26C03-FF93-11d0-817E-0000F87557DB),
        helpstring("DXTaskManager Class")
    ]
    coclass DXTaskManager
    {
        [default] interface IDXTaskManager;
    };

    ///////////////////////////////
    // DXTScale CoClass
    ///////////////////////////////
    [
        uuid(555278E2-05DB-11D1-883A-3C8B00C10000),
        helpstring("DXTScale Class")
    ]
    coclass DXTScale
    {
        [default] interface IDXTScale;
    };

    ///////////////////////////////
    // DXSurface CoClass
    ///////////////////////////////
    [
	uuid(0E890F83-5F79-11D1-9043-00C04FD9189D),
	helpstring("DXSurface Class")
    ]
    coclass DXSurface
    {
	[default] interface IDXSurface;
    };

    ///////////////////////////////
    // DXSurfaceModifier CoClass
    ///////////////////////////////
    [
	uuid(3E669F1D-9C23-11d1-9053-00C04FD9189D),
	helpstring("DXSurfaceModifier Class")
    ]
    coclass DXSurfaceModifier
    {
	[default] interface IDXSurfaceModifier;
    };

    ///////////////////////////////
    // DXGradient CoClass
    ///////////////////////////////
    [
	uuid(C6365470-F667-11d1-9067-00C04FD9189D),
	helpstring("DXGradient Class")
    ]
    coclass DXGradient
    {
        [default] interface IDXGradient;
    };
};
