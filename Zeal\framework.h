#pragma once
// Windows Header Files
#include <windows.h>
#include "hook_wrapper.h"
#include "EqFunctions.h"
//hooks
#include "commands.h"
#include "camera_mods.h"
#include "looting.h"
#include "FindPattern.h"
#include "labels.h"
#include "binds.h"
#include "raid.h"
#include "eqstr.h"
#include "equip_item.h"
#include "chat.h"
#include "chatfilter.h"
#include "IO_ini.h"
#include "callbacks.h"
#include "item_display.h"
#include "melody.h"
#include "named_pipe.h"
#include "floating_damage.h"
#include "directx.h"
#include "nameplate.h"
#include "tellwindows.h"
#include "helm_manager.h"
#include "music.h"
#include "tick.h"
#include "character_select.h"
#include "survey.h"
// other features
#include "NPCGive.h"
#include "cycle_target.h"
#include "assist.h"
#include "outputfile.h"
#include "experience.h"
#include "buff_timers.h"
#include "player_movement.h"
#include "spellsets.h"
#include "alarm.h"
#include "netstat.h"
#include "ui_manager.h"
#include "autofire.h"
#include "tooltip.h"
#include "physics.h"
#include "target_ring.h"
#include "zone_map.h"
#include "crash_handler.h"
#include "EntityManager.h"
#include "patches.h"
#include "charm_monitor.h"

//#include "ZealSettings.h"
//#include "Zeal.h"


extern HMODULE this_module;
