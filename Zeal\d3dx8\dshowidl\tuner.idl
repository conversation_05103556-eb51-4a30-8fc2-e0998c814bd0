//------------------------------------------------------------------------------
// File: Tuner.idl
//
// Desc: 
//
// Copyright (c) 1999 - 2000, Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------------------------


cpp_quote("//+-------------------------------------------------------------------------")
cpp_quote("//")
cpp_quote("//  Microsoft Windows")
cpp_quote("//  Copyright (C) Microsoft Corporation, 1999-2000.")
cpp_quote("//")
cpp_quote("//--------------------------------------------------------------------------")
cpp_quote("#pragma once")

#include <olectl.h>

#ifndef DO_NO_IMPORTS
import "oaidl.idl";
import "comcat.idl";
import "strmif.idl";
import "bdatypes.h";
import "regbag.idl";
#endif

cpp_quote("#include <bdatypes.h>")

interface ITuningSpaceContainer;
interface ITuningSpace;
interface IEnumTuningSpaces;
interface ITuneRequest;
interface ITuner;
interface IScanningTuner;
interface ITunerEvents;
interface IEnumComponentTypes;
interface IComponentTypes;
interface IComponentType;
interface ILanguageComponentType;
interface IEnumComponents;
interface IComponents;
interface IComponent;
interface IMPEG2ComponentType;
interface IMPEG2Component;
interface ILocator;
interface IATSCLocator;
interface IDVBSLocator;
interface IDVBTLocator;

// tuner.idl:
//  interface ITuningSpaceContainer : IDispatch
//  interface ITuningSpace : IDispatch
//  interface ITuneRequest : IDispatch
//  interface IComponentType : IDispatch
//  interface ILanguageComponentType : IComponentType
//  interface IComponentTypes : IDispatch
//  interface IComponent : IDispatch
//  interface IComponents : IDispatch
//  interface ITVTuningSpace : ITuningSpace
//  interface IFMRadioTuningSpace : ITuningSpace
//  interface IAMRadioTuningSpace : ITuningSpace
//  interface IAnalogTVTuningSpace : ITVTuningSpace
//  interface IAnalogTVAudioComponent : IComponent
//  interface IAnalogTVDataComponent : IComponent
//  interface IChannelTuneRequest : ITuneRequest
//  interface IDVBComponent : IComponent
//  interface IDVBTuneRequest : ITuneRequest

// DISPIDs for ITuningSpaces interface
// we don't expect to inherit from this interface and consequently there's
//     no chance of collision, thus we'll allow midl to auto assign ids for this one

// DISPIDs for ITuningSpace interface
enum {
DISPID_TUNER_TS_UNIQUENAME                      = 1,
DISPID_TUNER_TS_FRIENDLYNAME                    = 2,
DISPID_TUNER_TS_CLSID		                    = 3,
DISPID_TUNER_TS_NETWORKTYPE                     = 4,
DISPID_TUNER_TS__NETWORKTYPE                    = 5,
DISPID_TUNER_TS_CREATETUNEREQUEST               = 6,
DISPID_TUNER_TS_ENUMCATEGORYGUIDS               = 7,
DISPID_TUNER_TS_ENUMDEVICEMONIKERS              = 8,
DISPID_TUNER_TS_DEFAULTPREFERREDCOMPONENTTYPES  = 9,
DISPID_TUNER_TS_FREQMAP							= 10,
DISPID_TUNER_TS_DEFLOCATOR						= 11,
DISPID_TUNER_TS_CLONE							= 12,

// DISPIDs for ITuneRequest interface
DISPID_TUNER_TR_TUNINGSPACE                     = 1,
DISPID_TUNER_TR_COMPONENTS                      = 2,
DISPID_TUNER_TR_CLONE                           = 3,
DISPID_TUNER_TR_LOCATOR                         = 4,


// DISPID for IComponentType interface
DISPID_TUNER_CT_CATEGORY                        = 1,
DISPID_TUNER_CT_MEDIAMAJORTYPE			= 2,
DISPID_TUNER_CT__MEDIAMAJORTYPE                 = 3,
DISPID_TUNER_CT_MEDIASUBTYPE		       	= 4,
DISPID_TUNER_CT__MEDIASUBTYPE		        = 5,
DISPID_TUNER_CT_MEDIAFORMATTYPE 		= 6,
DISPID_TUNER_CT__MEDIAFORMATTYPE 		= 7,
DISPID_TUNER_CT_MEDIATYPE			= 8,
DISPID_TUNER_CT_CLONE                           = 9,


// DISPID for ILanguageComponentType interface
DISPID_TUNER_LCT_LANGID                         = 100,

// DISPID for IMPEG2ComponentType interface
DISPID_TUNER_MP2CT_TYPE                         = 200,

// DISPID for IATSCComponentType interface
DISPID_TUNER_ATSCCT_FLAGS                       = 300,

// DISPID for ILocator interface
DISPID_TUNER_L_CARRFREQ                         = 1,
DISPID_TUNER_L_INNERFECMETHOD                   = 2,
DISPID_TUNER_L_INNERFECRATE                     = 3,
DISPID_TUNER_L_OUTERFECMETHOD                   = 4,
DISPID_TUNER_L_OUTERFECRATE                     = 5,
DISPID_TUNER_L_MOD                              = 6,
DISPID_TUNER_L_SYMRATE                          = 7,
DISPID_TUNER_L_CLONE                            = 8,

// DISPID for IATSCLocator interface
DISPID_TUNER_L_ATSC_PHYS_CHANNEL                = 201,
DISPID_TUNER_L_ATSC_TSID                        = 202,

// DISPID for IDVBTLocator interface
DISPID_TUNER_L_DVBT_BANDWIDTH                   = 301,
DISPID_TUNER_L_DVBT_LPINNERFECMETHOD            = 302,
DISPID_TUNER_L_DVBT_LPINNERFECRATE              = 303,
DISPID_TUNER_L_DVBT_GUARDINTERVAL               = 304,
DISPID_TUNER_L_DVBT_HALPHA                      = 305,
DISPID_TUNER_L_DVBT_TRANSMISSIONMODE            = 306,
DISPID_TUNER_L_DVBT_INUSE                       = 307,

// DISPID for IDVBSLocator interface
DISPID_TUNER_L_DVBS_POLARISATION                = 401,
DISPID_TUNER_L_DVBS_WEST                        = 402,
DISPID_TUNER_L_DVBS_ORBITAL                     = 403,
DISPID_TUNER_L_DVBS_AZIMUTH                     = 404,
DISPID_TUNER_L_DVBS_ELEVATION                   = 405,

// DISPID for IDVBCLocator interface

// DISPIDs for IComponent interface
DISPID_TUNER_C_TYPE                             = 1,
DISPID_TUNER_C_STATUS                           = 2,
DISPID_TUNER_C_LANGID                           = 3,
DISPID_TUNER_C_DESCRIPTION                      = 4,
DISPID_TUNER_C_CLONE                            = 5,

// DISPIDs for IMPEG2Component interface
DISPID_TUNER_C_MP2_PID                           = 101,
DISPID_TUNER_C_MP2_PCRPID                        = 102,
DISPID_TUNER_C_MP2_PROGNO                        = 103,

// DISPIDs for IDVBTuningSpace interface
DISPID_TUNER_TS_DVB_SYSTEMTYPE                = 101,

// DISPIDs for IAnalogRadioTuningSpace interface
DISPID_TUNER_TS_AR_MINFREQUENCY               = 101,
DISPID_TUNER_TS_AR_MAXFREQUENCY               = 102,
DISPID_TUNER_TS_AR_STEP                       = 103,

// DISPIDs for IAnalogTVTuningSpace interface
DISPID_TUNER_TS_ATV_MINCHANNEL                 = 101,
DISPID_TUNER_TS_ATV_MAXCHANNEL                 = 102,
DISPID_TUNER_TS_ATV_INPUTTYPE                  = 103,
DISPID_TUNER_TS_ATV_COUNTRYCODE                = 104,

// DISPIDs for IATSCTuningSpace interface
DISPID_TUNER_TS_ATSC_MINMINORCHANNEL           = 201,
DISPID_TUNER_TS_ATSC_MAXMINORCHANNEL           = 202,
DISPID_TUNER_TS_ATSC_MINPHYSCHANNEL            = 203,
DISPID_TUNER_TS_ATSC_MAXPHYSCHANNEL            = 204,

// DISPID for IAnalogTVAudioComponent interface
DISPID_CHTUNER_ATVAC_CHANNEL                    = 101,

// DISPIDs for IAnalogTVDataComponent interface
DISPID_CHTUNER_ATVDC_SYSTEM                     = 101,
DISPID_CHTUNER_ATVDC_CONTENT                    = 102,

// DISPID for IChannelTuneRequest interface
DISPID_CHTUNER_CTR_CHANNEL                      = 101,

// DISPID IATSCChannelTuneRequest
DISPID_CHTUNER_ACTR_MINOR_CHANNEL               = 201,

// DISPIDs for IDVBComponent interface
DISPID_DVBTUNER_DVBC_ATTRIBUTESVALID            = 101,
DISPID_DVBTUNER_DVBC_PID                        = 102,
DISPID_DVBTUNER_DVBC_TAG                        = 103,
DISPID_DVBTUNER_DVBC_COMPONENTTYPE              = 104,

// DISPIDs for IDVBTuneRequest interface
DISPID_DVBTUNER_ONID         = 101,
DISPID_DVBTUNER_TSID         = 102,
DISPID_DVBTUNER_SID          = 103,

};


//////////////////////////////////////////////////////////////////////////////////////
// Tuning Space Container
//////////////////////////////////////////////////////////////////////////////////////
// simple read-only collection of tuning spaces for enumerating subsets of the total
// set of system tuning spaces
    [
        object,
        uuid(901284E4-33FE-4b69-8D63-634A596F3756),
        dual,
        oleautomation,
        nonextensible,
        helpstring("Tuning Space Collection Interface"),
        pointer_default(unique)
    ]
    interface ITuningSpaces : IDispatch
    {

        [propget, helpstring("Number of items in the collection")]
        HRESULT Count(
            [out, retval] long *Count
            );

        [propget, id(DISPID_NEWENUM), hidden, restricted]
        HRESULT _NewEnum(
            [out, retval] IEnumVARIANT** NewEnum
            );

        [id(DISPID_VALUE),
            propget,
            helpstring("Find the Tuning Space with the specified Index")]
        HRESULT Item(
            [in] VARIANT varIndex,
            [out, retval] ITuningSpace** TuningSpace
            );

        [propget, hidden, restricted, 
			helpstring("convenience function so C++ apps don't have to unpack VARIANT")]
        HRESULT EnumTuningSpaces(
            [out, retval] IEnumTuningSpaces** NewEnum
            );

	}

//////////////////////////////////////////////////////////////////////////////////////
// Tuning Space Container
//////////////////////////////////////////////////////////////////////////////////////
    [
        object,
        uuid(5B692E84-E2F1-11d2-9493-00C04F72D980),
        dual,
        oleautomation,
        hidden,
        nonextensible,
        helpstring("Tuning Space Container Interface"),
        pointer_default(unique)
    ]
    interface ITuningSpaceContainer : IDispatch
    {

        [propget, helpstring("Number of items in the collection")]
        HRESULT Count(
            [out, retval] long *Count
            );

        [propget, id(DISPID_NEWENUM), hidden, restricted]
        HRESULT _NewEnum(
            [out, retval] IEnumVARIANT** NewEnum
            );

        [id(DISPID_VALUE),
            propget,
            helpstring("Find the Tuning Space with the specified Index")]
        HRESULT Item(
            [in] VARIANT varIndex,
            [out, retval] ITuningSpace** TuningSpace
            );
        [id(DISPID_VALUE),
            propput,
            helpstring("Change the Tuning Space with the specified Index")]
        HRESULT Item([in] VARIANT varIndex, [in] ITuningSpace *TuningSpace);

        [helpstring("Returns the collection of Tuning Spaces with the same implementation")]
        HRESULT TuningSpacesForCLSID(
            [in] BSTR SpaceCLSID,
            [out, retval] ITuningSpaces** NewColl
            );

        [hidden, restricted, 
			helpstring("Convenience Function for enumerating from C")]
        HRESULT _TuningSpacesForCLSID(
            [in] REFCLSID SpaceCLSID,
            [out, retval] ITuningSpaces** NewColl
            );

        [helpstring("Returns the collection of Tuning Spaces matching the name")]
        HRESULT TuningSpacesForName(
            [in] BSTR Name,
            [out, retval] ITuningSpaces** NewColl
            );

        [helpstring("Find Local ID Number of the specified Tuning Space")]
        HRESULT FindID(
            [in] ITuningSpace *TuningSpace,
            [out, retval] long *ID
            );


        [id(DISPID_ADDITEM), 
		 helpstring("Add a new Tuning Space to the collection.  This tuning space will be persisted unless removed")]
        HRESULT Add(
            [in] ITuningSpace* TuningSpace,
            [out, retval] VARIANT* NewIndex
            );

        [propget, hidden, restricted, helpstring("Convenience Function for enumerating collection from C")]
        HRESULT EnumTuningSpaces(
            [out, retval] IEnumTuningSpaces **ppEnum
            );

        [id(DISPID_REMOVEITEM), 
		 helpstring("Remove a Tuning Space from the collection.  this tuning space will be deleted from the registry")]
        HRESULT Remove(
            [in] VARIANT Index
            );

	    [propget, helpstring("Maximum number of items allowed in the collection")]
        HRESULT MaxCount(
            [out, retval] long *MaxCount
            );

		// we don't allow script to set this, but we do allow c code to change it.
	    [propput, hidden, restricted, helpstring("Maximum number of items allowed in the collection")]
        HRESULT MaxCount(
            [in] long MaxCount
            );

    }

//////////////////////////////////////////////////////////////////////////////////////
// Tuning Space Interfaces
//////////////////////////////////////////////////////////////////////////////////////

// note: the network type is the clsid of the tuning space implementation
    [
        object,
        uuid(061C6E30-E622-11d2-9493-00C04F72D980),
        dual,
        oleautomation,
        nonextensible,
        helpstring("Tuning Space Interface"),
        pointer_default(unique)
    ]
    interface ITuningSpace : IDispatch
    {
        [propget, id(DISPID_TUNER_TS_UNIQUENAME),
             helpstring("Unique name of the Tuning Space")]
        HRESULT UniqueName(
            [out, retval] BSTR *Name
            );

        [propput, id(DISPID_TUNER_TS_UNIQUENAME),
             helpstring("Unique name of the Tuning Space")]
        HRESULT UniqueName(
            [in] BSTR Name
            );

        [propget, id(DISPID_TUNER_TS_FRIENDLYNAME),
             helpstring("User-friendly name of the Tuning Space")]
        HRESULT FriendlyName(
            [out, retval] BSTR *Name
            );

        [propput, id(DISPID_TUNER_TS_FRIENDLYNAME),
             helpstring("User-friendly name of the Tuning Space")]
        HRESULT FriendlyName(
            [in] BSTR Name
            );

        [propget, id(DISPID_TUNER_TS_CLSID), 
			helpstring("Returns the clsid of the tuning space implementation.  provides script access to IPersist:GetClassID")]
        HRESULT CLSID(
            [out, retval] BSTR* SpaceCLSID
            );

        [propget, id(DISPID_TUNER_TS_NETWORKTYPE),
             helpstring("Network Type (Network Proivder CLSID)")]
        HRESULT NetworkType(
            [out, retval] BSTR *NetworkTypeGuid
            );
        [propput, id(DISPID_TUNER_TS_NETWORKTYPE),
             helpstring("Network Type (Network Proivder CLSID)")]
        HRESULT NetworkType(
            [in] BSTR NetworkTypeGuid
            );

        [propget, id(DISPID_TUNER_TS__NETWORKTYPE),
             helpstring("Network Type (Network Proivder CLSID)"), hidden, restricted]
        HRESULT _NetworkType(
            [out, retval] GUID* NetworkTypeGuid
            );
        [propput, id(DISPID_TUNER_TS__NETWORKTYPE),
             helpstring("Network Type (Network Proivder CLSID)"), hidden, restricted]
        HRESULT _NetworkType(
            [in] REFCLSID NetworkTypeGuid
            );

        [id(DISPID_TUNER_TS_CREATETUNEREQUEST),
            helpstring("Create a Tune Request object")]
        HRESULT CreateTuneRequest(
            [out, retval] ITuneRequest **TuneRequest
            );

        [id(DISPID_TUNER_TS_ENUMCATEGORYGUIDS), hidden, restricted]
        HRESULT EnumCategoryGUIDs(
            [out, retval] IEnumGUID **ppEnum
            );

        [id(DISPID_TUNER_TS_ENUMDEVICEMONIKERS), hidden, restricted]
        HRESULT EnumDeviceMonikers(
            [out, retval] IEnumMoniker **ppEnum
            );

        [propget, id(DISPID_TUNER_TS_DEFAULTPREFERREDCOMPONENTTYPES)]
        HRESULT DefaultPreferredComponentTypes(
            [out, retval] IComponentTypes** ComponentTypes
            );
        [propput, id(DISPID_TUNER_TS_DEFAULTPREFERREDCOMPONENTTYPES)]
        HRESULT DefaultPreferredComponentTypes(
            [in] IComponentTypes* NewComponentTypes
            );

		// the following property is for the network providers to store frequency/channel maps, frequency/transponder maps
		// or whatever other mapping from carrier frequencies to frequency identifiers are appropriate for the tuning space
		// in question.  the bstr is treated as a binary blob.  it is expected to contain embedded nulls,  and it may be formatted
		// internally in whatever fashion the network provider sees fit.
        [propget, id(DISPID_TUNER_TS_FREQMAP), hidden, restricted]
        HRESULT FrequencyMapping([out, retval] BSTR *pMapping);
        [propput, id(DISPID_TUNER_TS_FREQMAP), hidden, restricted]
        HRESULT FrequencyMapping(BSTR Mapping);

		// the following property provides a starting point for the initial IScanningTuner after installation
        [propget, id(DISPID_TUNER_TS_DEFLOCATOR)]
        HRESULT DefaultLocator([out, retval] ILocator **LocatorVal);
        [propput, id(DISPID_TUNER_TS_DEFLOCATOR)]
        HRESULT DefaultLocator([in]ILocator *LocatorVal);

		HRESULT Clone([out, retval] ITuningSpace **NewTS);
    }

//////////////////////////////////////////////////////////////////////////////////////
    [
        hidden,
        restricted,
        object,
        uuid(8B8EB248-FC2B-11d2-9D8C-00C04F72D980),
        pointer_default(unique)
    ]
    interface IEnumTuningSpaces : IUnknown
    {
        HRESULT Next(
            [in] ULONG celt,
            [out, size_is(celt), length_is(*pceltFetched)]
                ITuningSpace** rgelt,
            [out] ULONG* pceltFetched
            );

        HRESULT Skip(
            [in] ULONG celt
            );

        HRESULT Reset(void);

        HRESULT Clone(
            [out] IEnumTuningSpaces** ppEnum
            );
    }

//////////////////////////////////////////////////////////////////////////////////////
    [
        object,
        hidden,
        nonextensible,
        uuid(ADA0B268-3B19-4e5b-ACC4-49F852BE13BA),
        pointer_default(unique)
    ]
    interface IDVBTuningSpace : ITuningSpace
    {
        [propget, id(DISPID_TUNER_TS_DVB_SYSTEMTYPE),
         helpstring("Type of DVB System")
        ]
        HRESULT SystemType(
            [out, retval] DVBSystemType *SysType
            );
        [propput, id(DISPID_TUNER_TS_DVB_SYSTEMTYPE),
         helpstring("Type of DVB System")
        ]
        HRESULT SystemType(
            [in] DVBSystemType SysType
            );

    }


//////////////////////////////////////////////////////////////////////////////////////
    [
        object,
        hidden,
        nonextensible,
        uuid(2A6E293C-2595-11d3-B64C-00C04F79498E),
        pointer_default(unique)
    ]
    interface IAnalogTVTuningSpace : ITuningSpace
    {
        [propget, id(DISPID_TUNER_TS_ATV_MINCHANNEL),
         helpstring("Smallest channel number ever provided by this tuning space")
        ]
        HRESULT MinChannel(
            [out, retval] long *MinChannelVal
            );
        [propput, id(DISPID_TUNER_TS_ATV_MINCHANNEL),
         helpstring("Smallest channel number ever provided by this tuning space")
        ]
        HRESULT MinChannel(
            [in] long NewMinChannelVal
            );
        [propget, id(DISPID_TUNER_TS_ATV_MAXCHANNEL),
         helpstring("Largest channel number ever provided by this tuning space")
        ]
        HRESULT MaxChannel(
            [out, retval] long *MaxChannelVal
            );
        [propput, id(DISPID_TUNER_TS_ATV_MAXCHANNEL),
         helpstring("Largest channel number ever provided by this tuning space")
        ]
        HRESULT MaxChannel(
            [in] long NewMaxChannelVal
            );
        [propget, id(DISPID_TUNER_TS_ATV_INPUTTYPE),
         helpstring("Input type for this tuning space")
        ]
        HRESULT InputType([out, retval] TunerInputType *InputTypeVal);
        [propput, id(DISPID_TUNER_TS_ATV_INPUTTYPE),
         helpstring("Input type for this tuning space")
        ]
        HRESULT InputType([in] TunerInputType NewInputTypeVal);
        [propget, id(DISPID_TUNER_TS_ATV_COUNTRYCODE),
         helpstring("International dialing prefix for country of physical broadcast source")
        ]
        HRESULT CountryCode([out, retval] long *CountryCodeVal);
        [propput, id(DISPID_TUNER_TS_ATV_COUNTRYCODE),
         helpstring("International dialing prefix for country of physical broadcast source")
        ]
        HRESULT CountryCode([in] long NewCountryCodeVal);
    }

//////////////////////////////////////////////////////////////////////////////////////
    [
        object,
        hidden,
        nonextensible,
        uuid(0369B4E2-45B6-11d3-B650-00C04F79498E),
        pointer_default(unique)
    ]
// note: we inherit ATSC from analog because we need the same properties.
// - the definition of channel is overloaded to be the major channel,
// - input type allows us to distinguish between broadcast and cable frequencies
// which allows us to use the atsc tuning space class for "open cable".
// - country code will allow us to redefine physical channel frequencies
// if any other countries adopt ATSC which may happen in Latin America in particular.
    interface IATSCTuningSpace : IAnalogTVTuningSpace
    {
        [propget, id(DISPID_TUNER_TS_ATSC_MINMINORCHANNEL),
         helpstring("Smallest minor channel number ever provided by this tuning space")
        ]
        HRESULT MinMinorChannel(
            [out, retval] long *MinMinorChannelVal
            );
        [propput, id(DISPID_TUNER_TS_ATSC_MINMINORCHANNEL),
         helpstring("Smallest minor channel number ever provided by this tuning space")
        ]
        HRESULT MinMinorChannel(
            [in] long NewMinMinorChannelVal
            );
        [propget, id(DISPID_TUNER_TS_ATSC_MAXMINORCHANNEL),
         helpstring("Largest minor channel number ever provided by this tuning space")
        ]
        HRESULT MaxMinorChannel(
            [out, retval] long *MaxMinorChannelVal
            );
        [propput, id(DISPID_TUNER_TS_ATSC_MAXMINORCHANNEL),
         helpstring("Largest minor channel number ever provided by this tuning space")
        ]
        HRESULT MaxMinorChannel(
            [in] long NewMaxMinorChannelVal
            );
        [propget, id(DISPID_TUNER_TS_ATSC_MINPHYSCHANNEL),
         helpstring("Smallest physical channel number ever provided by this tuning space")
        ]
        HRESULT MinPhysicalChannel(
            [out, retval] long *MinPhysicalChannelVal
            );
        [propput, id(DISPID_TUNER_TS_ATSC_MINPHYSCHANNEL),
         helpstring("Smallest physical channel number ever provided by this tuning space")
        ]
        HRESULT MinPhysicalChannel(
            [in] long NewMinPhysicalChannelVal
            );
        [propget, id(DISPID_TUNER_TS_ATSC_MAXPHYSCHANNEL),
         helpstring("Largest physical channel number ever provided by this tuning space")
        ]
        HRESULT MaxPhysicalChannel(
            [out, retval] long *MaxPhysicalChannelVal
            );
        [propput, id(DISPID_TUNER_TS_ATSC_MAXPHYSCHANNEL),
         helpstring("Largest physical channel number ever provided by this tuning space")
        ]
        HRESULT MaxPhysicalChannel(
            [in] long NewMaxPhysicalChannelVal
            );

    }

//////////////////////////////////////////////////////////////////////////////////////
    [
        object,
        hidden,
        nonextensible,
        uuid(2A6E293B-2595-11d3-B64C-00C04F79498E),
        pointer_default(unique)
    ]
    interface IAnalogRadioTuningSpace : ITuningSpace {
        [propget, id(DISPID_TUNER_TS_AR_MINFREQUENCY),
         helpstring("Smallest frequency(khz) ever used by this tuning space")
        ]
        HRESULT MinFrequency(
            [out, retval] long *MinFrequencyVal
            );
        [propput, id(DISPID_TUNER_TS_AR_MINFREQUENCY),
         helpstring("Smallest frequency(khz) ever used by this tuning space")
        ]
        HRESULT MinFrequency(
            [in] long NewMinFrequencyVal
            );
        [propget, id(DISPID_TUNER_TS_AR_MAXFREQUENCY),
         helpstring("Largest frequency(khz) ever used by this tuning space")
        ]
        HRESULT MaxFrequency(
            [out, retval] long *MaxFrequencyVal
            );
        [propput, id(DISPID_TUNER_TS_AR_MAXFREQUENCY),
         helpstring("Largest frequency(khz) ever used by this tuning space")
        ]
        HRESULT MaxFrequency(
            [in] long NewMaxFrequencyVal
            );
        [propget, id(DISPID_TUNER_TS_AR_STEP),
         helpstring("Default step value(khz) to next frequency for this tuning space")
        ]
        HRESULT Step(
            [out, retval] long *StepVal
            );
        [propput, id(DISPID_TUNER_TS_AR_STEP),
         helpstring("Default step value(khz) to next frequency for this tuning space")
        ]
        HRESULT Step(
            [in] long NewStepVal
            );      
    }


//////////////////////////////////////////////////////////////////////////////////////
// Tune Request Interfaces
//////////////////////////////////////////////////////////////////////////////////////
    [
        object,
        nonextensible,
        uuid(07DDC146-FC3D-11d2-9D8C-00C04F72D980),
        dual,
        oleautomation,
        helpstring("Tune Request Interface"),
        pointer_default(unique)
    ]
    interface ITuneRequest : IDispatch
    {
        [propget, id(DISPID_TUNER_TR_TUNINGSPACE),
            helpstring("Tuning Space object")]
        HRESULT TuningSpace(
            [out, retval] ITuningSpace **TuningSpace
            );
        [propget, id(DISPID_TUNER_TR_COMPONENTS),
            helpstring("Components collection")]
        HRESULT Components(
            [out, retval] IComponents **Components
            );
        [id(DISPID_TUNER_TR_CLONE),
            helpstring("Create a new copy of this tune request")]
        HRESULT Clone(
            [out, retval] ITuneRequest **NewTuneRequest
            );
        [propget, id(DISPID_TUNER_TR_LOCATOR),
            helpstring("Locator Object")]
        HRESULT Locator(
            [out, retval] ILocator **Locator
            );
        [propput, id(DISPID_TUNER_TR_LOCATOR),
            helpstring("Locator Object")]
        HRESULT Locator(
            [in] ILocator *Locator
            );
    }

//////////////////////////////////////////////////////////////////////////////////////
    [
        object,
        nonextensible,
        uuid(0369B4E0-45B6-11d3-B650-00C04F79498E),
        dual,
        oleautomation,
        helpstring("Channel Tune Request Interface"),
        pointer_default(unique)
    ]
    interface IChannelTuneRequest : ITuneRequest
    {
        [propget, id(DISPID_CHTUNER_CTR_CHANNEL),
            helpstring("Channel")]
        HRESULT Channel(
            [out, retval] long *Channel
            );
        [propput, id(DISPID_CHTUNER_CTR_CHANNEL),
            helpstring("Channel")]
        HRESULT Channel(
            [in] long Channel
            );
    }

//////////////////////////////////////////////////////////////////////////////////////
    [
        object,
        nonextensible,
        uuid(0369B4E1-45B6-11d3-B650-00C04F79498E),
        dual,
        oleautomation,
        helpstring("ATSC Channel Tune Request Interface"),
        pointer_default(unique)
    ]
    interface IATSCChannelTuneRequest : IChannelTuneRequest
    {
        [propget, id(DISPID_CHTUNER_ACTR_MINOR_CHANNEL),
            helpstring("Minor Channel")]
        HRESULT MinorChannel(
            [out, retval] long *MinorChannel
            );
        [propput, id(DISPID_CHTUNER_ACTR_MINOR_CHANNEL),
            helpstring("Minor Channel")]
        HRESULT MinorChannel(
            [in] long MinorChannel
            );
    }

//////////////////////////////////////////////////////////////////////////////////////
    [
        object,
        nonextensible,
        uuid(0D6F567E-A636-42bb-83BA-CE4C1704AFA2),
        dual,
        oleautomation,
        helpstring("DVB Tune Request Interface"),
        pointer_default(unique)
    ]
    interface IDVBTuneRequest : ITuneRequest
    {
        [propget, id(DISPID_DVBTUNER_ONID),
            helpstring("Original Network ID")]
        HRESULT ONID(
            [out, retval] long *ONID
            );
        [propput, id(DISPID_DVBTUNER_ONID),
            helpstring("Original Network ID")]
        HRESULT ONID(
            [in] long ONID
            );
        [propget, id(DISPID_DVBTUNER_TSID),
            helpstring("Transport Stream ID")]
        HRESULT TSID(
            [out, retval] long *TSID
            );
        [propput, id(DISPID_DVBTUNER_TSID),
            helpstring("Transport Stream ID")]
        HRESULT TSID(
            [in] long TSID
            );
        [propget, id(DISPID_DVBTUNER_SID),
            helpstring("Service ID")]
        HRESULT SID(
            [out, retval] long *SID
            );
        [propput, id(DISPID_DVBTUNER_SID),
            helpstring("Service ID")]
        HRESULT SID(
            [in] long SID
            );
    }

//////////////////////////////////////////////////////////////////////////////////////
// Tuner Interfaces
//////////////////////////////////////////////////////////////////////////////////////
    [
        object,
        hidden,
        nonextensible,
        uuid(28C52640-018A-11d3-9D8E-00C04F72D980),
        helpstring("Tuner Interface"),
        pointer_default(unique)
    ]
    interface ITuner : IUnknown
    {
        [propget, helpstring("Tuning Space object")]
        HRESULT TuningSpace(
            [out, retval] ITuningSpace **TuningSpace
            );

        [propput, helpstring("Tuning Space object")]
        HRESULT TuningSpace(
            [in] ITuningSpace *TuningSpace
            );

        [hidden, restricted, helpstring("Returns an enumerator for Tuning Spaces accepted by this tuner")]
        HRESULT EnumTuningSpaces(
            [out, retval] IEnumTuningSpaces **ppEnum
            );

        [propget, helpstring("Tune Request object")]
        HRESULT TuneRequest(
            [out, retval] ITuneRequest **TuneRequest
            );

        [propput, helpstring("Tune Request object")]
        HRESULT TuneRequest(
            [in] ITuneRequest *TuneRequest
            );

        [helpstring("Validate the tuning request without tuning")]
        HRESULT Validate(
            [in] ITuneRequest *TuneRequest
            );

        [propget, helpstring("Preferred Component Types collection")]
        HRESULT PreferredComponentTypes(
            [out, retval] IComponentTypes **ComponentTypes
            );

        [propput, helpstring("Preferred Component Types collection")]
        HRESULT PreferredComponentTypes(
            [in] IComponentTypes *ComponentTypes
            );

        [propget, helpstring("Signal Strength")]
        HRESULT SignalStrength(
            [out, retval] long *Strength
            );

        [helpstring("Trigger Signal events (interval in milliseconds; 0 turns off trigger)")]
        HRESULT TriggerSignalEvents(
            [in] long Interval
            );
    }

//////////////////////////////////////////////////////////////////////////////////////
    [
        object,
        hidden,
        nonextensible,
        uuid(1DFD0A5C-0284-11d3-9D8E-00C04F72D980),
        helpstring("Scanning Tuner Interface"),
        pointer_default(unique)
    ]
    interface IScanningTuner : ITuner
    {
        [helpstring("Seek to the next valid selection")]
        HRESULT SeekUp(
            );

        [helpstring("Seek to the previous valid selection")]
        HRESULT SeekDown(
            );

        [helpstring("Scan for valid selections upward")]
        HRESULT ScanUp(
            [in] long MillisecondsPause
            );

        [helpstring("Scan for valid selections downward")]
        HRESULT ScanDown(
            [in] long MillisecondsPause
            );

        [helpstring("Enter the device's autoprogram mode")]
        HRESULT AutoProgram(
            );
    };

//////////////////////////////////////////////////////////////////////////////////////
    [
        object,
        hidden,
        nonextensible,
        uuid(*************-11d3-9D8E-00C04F72D980),
        helpstring("Tuner Events Interface"),
        pointer_default(unique)
    ]
    interface ITunerEvents : IUnknown
    {
        [helpstring("Reports a completed (or failed) tune operation")]
        HRESULT OnTune(
            [in] ITuneRequest **Request,
            [in] long *Strength
            );
    }

//////////////////////////////////////////////////////////////////////////////////////
    [
        object,
        hidden,
        nonextensible,
        uuid(85E2439E-0E23-11d3-9D8E-00C04F72D980),
        helpstring("Signal Events Interface"),
        pointer_default(unique)
    ]
    interface ISignalEvents : IUnknown
    {
        [helpstring("Reports the signal strength")]
        HRESULT OnSignal(
            [out] long *Strength
            );
    }

//////////////////////////////////////////////////////////////////////////////////////
// Component Type Interfaces
//////////////////////////////////////////////////////////////////////////////////////

    // Component Category

    [
        object,
        hidden,
        nonextensible,
        uuid(6A340DC0-0311-11d3-9D8E-00C04F72D980),
        dual,
        oleautomation,
        helpstring("Component Type Interface"),
        pointer_default(unique)
    ]
    interface IComponentType : IDispatch
    {
        [propget, id(DISPID_TUNER_CT_CATEGORY),
            helpstring("General category of component")]
        HRESULT Category(
            [out, retval] ComponentCategory *Category
            );
        [propput, id(DISPID_TUNER_CT_CATEGORY),
            helpstring("General category of component")]
        HRESULT Category(
            [in] ComponentCategory Category
            );

        [propget, id(DISPID_TUNER_CT_MEDIAMAJORTYPE),
            helpstring("DirectShow MediaType Major Type Guid")]
        HRESULT MediaMajorType(
            [out, retval] BSTR *MediaMajorType
            );    
        [propput, id(DISPID_TUNER_CT_MEDIAMAJORTYPE),
            helpstring("DirectShow MediaType Major Type Guid")]
        HRESULT MediaMajorType(
            [in] BSTR MediaMajorType
            );
        [propget, id(DISPID_TUNER_CT__MEDIAMAJORTYPE),
            helpstring("DirectShow MediaType Major Type Guid"), hidden, restricted]
        HRESULT _MediaMajorType(
            [out, retval] GUID* MediaMajorTypeGuid
            );
        [propput, id(DISPID_TUNER_CT__MEDIAMAJORTYPE),
            helpstring("DirectShow MediaType Major Type Guid"), hidden, restricted]
        HRESULT _MediaMajorType(
            [in] REFCLSID MediaMajorTypeGuid
            );
        [propget, id(DISPID_TUNER_CT_MEDIASUBTYPE),
            helpstring("DirectShow MediaType Sub Type Guid")]
        HRESULT MediaSubType(
            [out, retval] BSTR *MediaSubType
            );
        [propput, id(DISPID_TUNER_CT_MEDIASUBTYPE),
            helpstring("DirectShow MediaType Sub Type Guid")]
        HRESULT MediaSubType(
            [in] BSTR MediaSubType
            );
        [propget, id(DISPID_TUNER_CT__MEDIASUBTYPE),
            helpstring("DirectShow MediaType Sub Type Guid"), hidden, restricted]
        HRESULT _MediaSubType(
            [out, retval] GUID* MediaSubTypeGuid
            );
        [propput, id(DISPID_TUNER_CT__MEDIASUBTYPE),
            helpstring("DirectShow MediaType Sub Type Guid"), hidden, restricted]
        HRESULT _MediaSubType(
            [in] REFCLSID MediaSubTypeGuid
            );
        [propget, id(DISPID_TUNER_CT_MEDIAFORMATTYPE),
            helpstring("DirectShow MediaType Format Guid")]
        HRESULT MediaFormatType(
            [out, retval] BSTR *MediaFormatType
            );
        [propput, id(DISPID_TUNER_CT_MEDIAFORMATTYPE),
            helpstring("DirectShow MediaType Format Guid")]
        HRESULT MediaFormatType(
            [in] BSTR MediaFormatType
            );
        [propget, id(DISPID_TUNER_CT__MEDIAFORMATTYPE),
            helpstring("DirectShow MediaType Format Guid"), hidden, restricted]
        HRESULT _MediaFormatType(
            [out, retval] GUID* MediaFormatTypeGuid
            );
        [propput, id(DISPID_TUNER_CT__MEDIAFORMATTYPE),
            helpstring("DirectShow MediaType Format Guid"), hidden, restricted]
        HRESULT _MediaFormatType(
            [in] REFCLSID MediaFormatTypeGuid
            );

        [propget, id(DISPID_TUNER_CT_MEDIATYPE), hidden, restricted,
            helpstring("DirectShow MediaType Guid, this only retrieves major, sub, format guids not the entire struct")]
        HRESULT MediaType(
            [out, retval] AM_MEDIA_TYPE *MediaType
            );
        [propput, id(DISPID_TUNER_CT_MEDIATYPE), hidden, restricted,
            helpstring("DirectShow MediaType Guid, this only retrieves major, sub, format guids not the entire struct")]
        HRESULT MediaType(
            [in] AM_MEDIA_TYPE *MediaType
            );

        [id(DISPID_TUNER_CT_CLONE),
            helpstring("Create a copy of this component type")]
        HRESULT Clone(
            [out, retval] IComponentType **NewCT
            );
    };

//////////////////////////////////////////////////////////////////////////////////////
    [
        object,
        hidden,
        nonextensible,
        uuid(B874C8BA-0FA2-11d3-9D8E-00C04F72D980),
        dual,
        oleautomation,
        helpstring("Language Component Type Interface"),
        pointer_default(unique)
    ]
    interface ILanguageComponentType : IComponentType
    {
        [propget, id(DISPID_TUNER_LCT_LANGID),
            helpstring("Language Identifier for Substream Content Language")]
        HRESULT LangID(
            [out, retval] long *LangID
            );

        [propput, id(DISPID_TUNER_LCT_LANGID),
            helpstring("Language Identifier for Substream Content Language")]
        HRESULT LangID(
            [in] long LangID
            );
    };

//////////////////////////////////////////////////////////////////////////////////////
    [
        object,
		hidden,
        nonextensible,
        uuid(2C073D84-B51C-48c9-AA9F-68971E1F6E38),
        dual,
        oleautomation,
        helpstring("MPEG2 Component Type Interface"),
        pointer_default(unique)
    ]
    interface IMPEG2ComponentType : ILanguageComponentType
    {
        [propget, id(DISPID_TUNER_MP2CT_TYPE),
            helpstring("MPEG2 Stream Type")]
        HRESULT StreamType(
            [out, retval] MPEG2StreamType *MP2StreamType
            );

        [propput, id(DISPID_TUNER_MP2CT_TYPE),
            helpstring("MPEG2 Stream Type")]
        HRESULT StreamType(
            [in] MPEG2StreamType MP2StreamType
            );
    };

//////////////////////////////////////////////////////////////////////////////////////

    [
        object,
        hidden,
        nonextensible,
        uuid(FC189E4D-7BD4-4125-B3B3-3A76A332CC96),
        dual,
        oleautomation,
        helpstring("ATSC Component Type Interface"),
        pointer_default(unique)
    ]
    interface IATSCComponentType : IMPEG2ComponentType
    {
        [propget, id(DISPID_TUNER_ATSCCT_FLAGS),
            helpstring("ATSC Component Type Flags")]
        HRESULT Flags(
            [out, retval] long *Flags
            );

        [propput, id(DISPID_TUNER_ATSCCT_FLAGS),
            helpstring("ATSC Component Type Flags")]
        HRESULT Flags(
            [in] long flags
            );
    };

//////////////////////////////////////////////////////////////////////////////////////
    [
        hidden, restricted,
        object,
        uuid(8A674B4A-1F63-11d3-B64C-00C04F79498E),
        pointer_default(unique)
    ]
    interface IEnumComponentTypes : IUnknown
    {
        HRESULT Next(
            [in] ULONG celt,
            [out, size_is(celt), length_is(*pceltFetched)]
                IComponentType** rgelt,
            [out] ULONG* pceltFetched
            );

        HRESULT Skip(
            [in] ULONG celt
            );

        HRESULT Reset(void);

        HRESULT Clone(
            [out] IEnumComponentTypes** ppEnum
            );
    }


//////////////////////////////////////////////////////////////////////////////////////
// Component Type Container
//////////////////////////////////////////////////////////////////////////////////////

    [
        object,
        hidden,
        nonextensible,
        uuid(0DC13D4A-0313-11d3-9D8E-00C04F72D980),
        dual,
        oleautomation,
        helpstring("ComponentType Collection Interface"),
        pointer_default(unique)
    ]
    interface IComponentTypes : IDispatch
    {
        [propget, helpstring("Number of items in the collection")]
        HRESULT Count(
            [out, retval] long *Count
            );

        [propget, id(DISPID_NEWENUM), hidden, restricted]
        HRESULT _NewEnum(
            [out, retval] IEnumVARIANT **ppNewEnum
            );

        [hidden, restricted, helpstring("Convenience Function for Use with C")]
        HRESULT EnumComponentTypes(
            [out, retval] IEnumComponentTypes **ppNewEnum
            );

        [id(DISPID_VALUE),
            propget,
            helpstring("Get the ComponentType at the specified index")]
        HRESULT Item(
            [in] VARIANT Index,
            [out, retval] IComponentType **ComponentType
            );

        [id(DISPID_VALUE),
            propput,
            helpstring("Get the ComponentType at the specified index")]
        HRESULT Item(
            [in] VARIANT Index,
            [in] IComponentType *ComponentType
            );

        [id(DISPID_ADDITEM), 
		 helpstring("Append the ComponentType to the collection")]
        HRESULT Add(
            [in] IComponentType *ComponentType,
            [out, retval] VARIANT *NewIndex
            );

        [id(DISPID_REMOVEITEM), 
		 helpstring("Clear the collection")]
        HRESULT Remove(
            [in] VARIANT Index
            );

        [helpstring("Copy the collection")]
        HRESULT Clone([out, retval] IComponentTypes **NewList);
    };

//////////////////////////////////////////////////////////////////////////////////////
// Component Interfaces
//////////////////////////////////////////////////////////////////////////////////////

    [
        object,
        nonextensible,
        uuid(1A5576FC-0E19-11d3-9D8E-00C04F72D980),
        dual,
        oleautomation,
        helpstring("Component Interface"),
        pointer_default(unique)
    ]
    interface IComponent : IDispatch
    {
        [propget, id(DISPID_TUNER_C_TYPE),
            helpstring("Component Type")]
        HRESULT Type(
            [out, retval] IComponentType** CT
            );
    
        // only loaders initialize components
        [hidden, restricted, propput, id(DISPID_TUNER_C_TYPE),
            helpstring("Component Type")]
        HRESULT Type(
            [in] IComponentType* CT
            );

        // NOTE: this langid is *not* the same as the langid
        // in the componenttype(if the componenttype is a languagecomponenttype)
        // the langid in the component type is the language of the content this
        // component is describing.  the following langid in the component is the
        // language of the descriptive info in the component object.
        [propget, id(DISPID_TUNER_C_LANGID),
            helpstring("Language Identifier for Description Language")]
        HRESULT DescLangID(
            [out, retval] long *LangID
            );

        [propput, id(DISPID_TUNER_C_LANGID),
            helpstring("Language Identifier for Description Language")]
        HRESULT DescLangID(
            [in] long LangID
            );

        [propget, id(DISPID_TUNER_C_STATUS),
            helpstring("Component Status")]
        HRESULT Status(
            [out, retval] ComponentStatus *Status
            );

        [propput, id(DISPID_TUNER_C_STATUS),
            helpstring("Component Status")]
        HRESULT Status(
            [in] ComponentStatus Status
            );

        [propget, id(DISPID_TUNER_C_DESCRIPTION),
            helpstring("Component Description")]
        HRESULT Description(
            [out, retval] BSTR *Description
            );

        // restricted: only loaders will initialize components
        [hidden, restricted, propput, id(DISPID_TUNER_C_DESCRIPTION),
            helpstring("Component Description")]
        HRESULT Description(
            [in] BSTR Description
            );

        [id(DISPID_TUNER_C_CLONE),
            helpstring("Copy Component")]
        HRESULT Clone(
            [out, retval] IComponent **NewComponent
            );

    };

//////////////////////////////////////////////////////////////////////////////////////
    [
        object,
        nonextensible,
        uuid(1493E353-1EB6-473c-802D-8E6B8EC9D2A9),
        dual,
        oleautomation,
        helpstring("MPEG2 Component Interface"),
        pointer_default(unique)
    ]
    interface IMPEG2Component : IComponent
    {
        [propget, id(DISPID_TUNER_C_MP2_PID),
            helpstring("MPEG2 Packet ID for this Substream")]
        HRESULT PID(
            [out, retval] long *PID
            );

        [propput, id(DISPID_TUNER_C_MP2_PID),
            helpstring("MPEG2 Packet ID for this Substream")]
        HRESULT PID(
            [in] long PID
            );
        [propget, id(DISPID_TUNER_C_MP2_PCRPID),
            helpstring("MPEG2 Packet ID for this Substream's Timestamps")]
        HRESULT PCRPID(
            [out, retval] long *PCRPID
            );

        [propput, id(DISPID_TUNER_C_MP2_PCRPID),
            helpstring("MPEG2 Packet ID for this Substream's Timestamps")]
        HRESULT PCRPID(
            [in] long PCRPID
            );

        // program number provides reverse lookup to PAT
        [propget, id(DISPID_TUNER_C_MP2_PROGNO),
            helpstring("MPEG2 Program Number")]
        HRESULT ProgramNumber(
            [out, retval] long *ProgramNumber
            );

        [propput, id(DISPID_TUNER_C_MP2_PROGNO),
            helpstring("MPEG2 Program Number")]
        HRESULT ProgramNumber(
            [in] long ProgramNumber
            );
    };

//////////////////////////////////////////////////////////////////////////////////////
    [
        hidden, restricted,
        object,
        uuid(2A6E2939-2595-11d3-B64C-00C04F79498E),
        pointer_default(unique)
    ]
    interface IEnumComponents : IUnknown
    {
        HRESULT Next(
            [in] ULONG celt,
            [out, size_is(celt), length_is(*pceltFetched)]
                IComponent** rgelt,
            [out] ULONG* pceltFetched
            );

        HRESULT Skip(
            [in] ULONG celt
            );

        HRESULT Reset(void);

        HRESULT Clone(
            [out] IEnumComponents** ppEnum
            );
    }


//////////////////////////////////////////////////////////////////////////////////////
// Component Container
//////////////////////////////////////////////////////////////////////////////////////

    [
        object,
        nonextensible,
        uuid(FCD01846-0E19-11d3-9D8E-00C04F72D980),
        dual,
        oleautomation,
        helpstring("Component Collection Interface"),
        pointer_default(unique)
    ]
    interface IComponents : IDispatch
    {
        [propget, helpstring("Number of items in the collection")]
        HRESULT Count(
            [out, retval] long *Count
            );

        [propget, id(DISPID_NEWENUM), hidden, restricted]
        HRESULT _NewEnum(
            [out, retval] IEnumVARIANT **ppNewEnum
            );

        [hidden, restricted, helpstring("Convenience Function for Use with C")]
        HRESULT EnumComponents(
            [out, retval] IEnumComponents **ppNewEnum
            );

        [id(DISPID_VALUE),
            propget,
            helpstring("Get the Component at the specified index")]
        HRESULT Item(
            [in] VARIANT Index,
            [out, retval] IComponent **ppComponent
            );

        [id(DISPID_ADDITEM), 
		 helpstring("Add the Component to the collection")]
        HRESULT Add(
            [in] IComponent *Component,
            [out, retval] VARIANT *NewIndex
            );

        [id(DISPID_REMOVEITEM), 
		 helpstring("Remove the Component at the specified index")]
        HRESULT Remove(
            [in] VARIANT Index
            );

        [helpstring("Copy the collection")]
        HRESULT Clone([out, retval] IComponents **NewList);
    };

//////////////////////////////////////////////////////////////////////////////////////
// Component Container
//////////////////////////////////////////////////////////////////////////////////////
// this separates the what(general tune request properties) from the where
    


    [
        object,
        nonextensible,
        uuid(286D7F89-760C-4F89-80C4-66841D2507AA),
        dual,
        oleautomation,
        helpstring("Generic Locator Information"),
        pointer_default(unique)
    ]
    interface ILocator : IDispatch
    {

        [propget, id(DISPID_TUNER_L_CARRFREQ), helpstring("Carrier Frequency(DecaHertz)")]
        HRESULT CarrierFrequency(
            [out, retval] long* Frequency
            );
        [propput, id(DISPID_TUNER_L_CARRFREQ), helpstring("Carrier Frequency(DecaHertz)")]
        HRESULT CarrierFrequency(
            [in] long Frequency
            );
        [propget, id(DISPID_TUNER_L_INNERFECMETHOD), helpstring("Inner Fec Type")]
        HRESULT InnerFEC(
            [out, retval] FECMethod* FEC
            );
        [propput, id(DISPID_TUNER_L_INNERFECMETHOD), helpstring("Inner Fec Type")]
        HRESULT InnerFEC(
            [in] FECMethod FEC
            );
        [propget, id(DISPID_TUNER_L_INNERFECRATE), helpstring("Inner Fec Rate")]
        HRESULT InnerFECRate(
            [out, retval] BinaryConvolutionCodeRate* FEC
            );
        [propput, id(DISPID_TUNER_L_INNERFECRATE), helpstring("Inner Fec Rate")]
        HRESULT InnerFECRate(
            [in] BinaryConvolutionCodeRate FEC
            );
        [propget, id(DISPID_TUNER_L_OUTERFECMETHOD), helpstring("Outer Fec Type")]
        HRESULT OuterFEC(
            [out, retval] FECMethod* FEC
            );
        [propput, id(DISPID_TUNER_L_OUTERFECMETHOD), helpstring("Outer Fec Type")]
        HRESULT OuterFEC(
            [in] FECMethod FEC
            );
        [propget, id(DISPID_TUNER_L_OUTERFECRATE), helpstring("Outer Fec Rate")]
        HRESULT OuterFECRate(
            [out, retval] BinaryConvolutionCodeRate* FEC
            );
        [propput, id(DISPID_TUNER_L_OUTERFECRATE), helpstring("Outer Fec Rate")]
        HRESULT OuterFECRate(
            [in] BinaryConvolutionCodeRate FEC
            );
        [propget, id(DISPID_TUNER_L_MOD), helpstring("Modulation Type")]
        HRESULT Modulation(
            [out, retval] ModulationType* Modulation
            );
        [propput, id(DISPID_TUNER_L_MOD), helpstring("Modulation Type")]
        HRESULT Modulation(
            [in] ModulationType Modulation
            );
        [propget, id(DISPID_TUNER_L_SYMRATE), helpstring("Modulation Symbol Rate")]
        HRESULT SymbolRate(
            [out, retval] long* Rate
            );
        [propput, id(DISPID_TUNER_L_SYMRATE), helpstring("Modulation Symbol Rate")]
        HRESULT SymbolRate(
            [in] long Rate
            );
        [id(DISPID_TUNER_L_CLONE), helpstring("Copy the Locator")]
        HRESULT Clone([out, retval] ILocator **NewLocator);
    };

//////////////////////////////////////////////////////////////////////////////////////
    [
        object,
        hidden,
        nonextensible,
        uuid(BF8D986F-8C2B-4131-94D7-4D3D9FCC21EF),
        dual,
        oleautomation,
        helpstring("ATSC Locator Information"),
        pointer_default(unique)
    ]
    interface IATSCLocator : ILocator
    {
        [propget, id(DISPID_TUNER_L_ATSC_PHYS_CHANNEL),
            helpstring("Physical Channel")]
        HRESULT PhysicalChannel(
            [out, retval] long *PhysicalChannel
            );
        [propput, id(DISPID_TUNER_L_ATSC_PHYS_CHANNEL),
            helpstring("Physical Channel")]
        HRESULT PhysicalChannel(
            [in] long PhysicalChannel
            );
        [propget, id(DISPID_TUNER_L_ATSC_TSID),
            helpstring("Transport Stream ID")]
        HRESULT TSID(
            [out, retval] long *TSID
            );
        [propput, id(DISPID_TUNER_L_ATSC_TSID),
            helpstring("Transport Stream ID")]
        HRESULT TSID(
            [in] long TSID
            );
    };

//////////////////////////////////////////////////////////////////////////////////////

    [
        object,
        hidden,
        nonextensible,
        uuid(8664DA16-DDA2-42ac-926A-C18F9127C302),
        dual,
        oleautomation,
        helpstring("DVB Terrestrial Locator Information"),
        pointer_default(unique)
    ]
    interface IDVBTLocator : ILocator
    {
        [propget, id(DISPID_TUNER_L_DVBT_BANDWIDTH), helpstring("Bandwidth")]
        HRESULT Bandwidth(
            [out, retval] long* BandWidthVal
            );
        [propput, id(DISPID_TUNER_L_DVBT_BANDWIDTH), helpstring("Bandwidth")]
        HRESULT Bandwidth(
            [in] long BandwidthVal
            );

        [propget, id(DISPID_TUNER_L_DVBT_LPINNERFECMETHOD), helpstring("Inner Fec Type for Low Priority Stream")]
        HRESULT LPInnerFEC(
            [out, retval] FECMethod* FEC
            );
        [propput, id(DISPID_TUNER_L_DVBT_LPINNERFECMETHOD), helpstring("Inner Fec Type for Low Priority Stream")]
        HRESULT LPInnerFEC(
            [in] FECMethod FEC
            );
        [propget, id(DISPID_TUNER_L_DVBT_LPINNERFECRATE), helpstring("Inner Fec Rate for Low Priority Stream")]
        HRESULT LPInnerFECRate(
            [out, retval] BinaryConvolutionCodeRate* FEC
            );
        [propput, id(DISPID_TUNER_L_DVBT_LPINNERFECRATE), helpstring("Inner Fec Rate for Low Priority Stream")]
        HRESULT LPInnerFECRate(
            [in] BinaryConvolutionCodeRate FEC
            );
        [propget, id(DISPID_TUNER_L_DVBT_HALPHA), helpstring("Hierarchical Alpha")]
        HRESULT HAlpha(
            [out, retval] HierarchyAlpha* Alpha
            );
        [propput, id(DISPID_TUNER_L_DVBT_HALPHA), helpstring("Hierarchical Alpha")]
        HRESULT HAlpha(
            [in] HierarchyAlpha Alpha
            );
        [propget, id(DISPID_TUNER_L_DVBT_GUARDINTERVAL), helpstring("Guard Interval")]
        HRESULT Guard(
            [out, retval] GuardInterval* GI
            );
        [propput, id(DISPID_TUNER_L_DVBT_GUARDINTERVAL), helpstring("Guard Interval")]
        HRESULT Guard(
            [in] GuardInterval GI
            );
        [propget, id(DISPID_TUNER_L_DVBT_TRANSMISSIONMODE), helpstring("Transmission Mode")]
        HRESULT Mode(
            [out, retval] TransmissionMode* mode
            );
        [propput, id(DISPID_TUNER_L_DVBT_TRANSMISSIONMODE), helpstring("Transmission Mode")]
        HRESULT Mode(
            [in] TransmissionMode mode
            );
        [propget, id(DISPID_TUNER_L_DVBT_INUSE), helpstring("Hierarchical Alpha")]
        HRESULT OtherFrequencyInUse(
            [out, retval] VARIANT_BOOL* OtherFrequencyInUseVal
            );
        [propput, id(DISPID_TUNER_L_DVBT_INUSE), helpstring("Hierarchical Alpha")]
        HRESULT OtherFrequencyInUse(
            [in] VARIANT_BOOL OtherFrequencyInUseVal
            );
    };

//////////////////////////////////////////////////////////////////////////////////////
    [
        object,
        hidden,
        nonextensible,
        uuid(3D7C353C-0D04-45f1-A742-F97CC1188DC8),
        dual,
        oleautomation,
        helpstring("DVB Satellite Locator Information"),
        pointer_default(unique)
    ]
    interface IDVBSLocator : ILocator
    {

        [propget, id(DISPID_TUNER_L_DVBS_POLARISATION), helpstring("Signal Polarisation Type")]
        HRESULT SignalPolarisation(
            [out, retval] Polarisation* PolarisationVal
            );
        [propput, id(DISPID_TUNER_L_DVBS_POLARISATION), helpstring("Signal Polarisation Type")]
        HRESULT SignalPolarisation(
            [in] Polarisation PolarisationVal
            );
        [propget, id(DISPID_TUNER_L_DVBS_WEST), helpstring("VARIANT_TRUE means orbital position specifies west longitude")]
        HRESULT WestPosition(
            [out, retval] VARIANT_BOOL* WestLongitude
            );
        [propput, id(DISPID_TUNER_L_DVBS_WEST), helpstring("VARIANT_TRUE means orbital position specifies west longitude")]
        HRESULT WestPosition(
            [in] VARIANT_BOOL WestLongitude
            );
        [propget, id(DISPID_TUNER_L_DVBS_ORBITAL), helpstring("Longitude in tenths of a degree")]
        HRESULT OrbitalPosition(
            [out, retval] long* longitude
            );
        [propput, id(DISPID_TUNER_L_DVBS_ORBITAL), helpstring("Longitude in tenths of a degree")]
        HRESULT OrbitalPosition(
            [in] long longitude
            );
        [propget, id(DISPID_TUNER_L_DVBS_AZIMUTH), helpstring("Azimuth in tenths of a degree")]
        HRESULT Azimuth(
            [out, retval] long* Azimuth
            );
        [propput, id(DISPID_TUNER_L_DVBS_AZIMUTH), helpstring("Azimuth in tenths of a degree")]
        HRESULT Azimuth(
            [in] long Azimuth
            );
        [propget, id(DISPID_TUNER_L_DVBS_ELEVATION), helpstring("Elevation in tenths of a degree")]
        HRESULT Elevation(
            [out, retval] long* Elevation
            );
        [propput, id(DISPID_TUNER_L_DVBS_ELEVATION), helpstring("Elevation in tenths of a degree")]
        HRESULT Elevation(
            [in] long Elevation
            );
    };

//////////////////////////////////////////////////////////////////////////////////////
    [
        object,
        hidden,
        nonextensible,
        uuid(6E42F36E-1DD2-43c4-9F78-69D25AE39034),
        dual,
        oleautomation,
        helpstring("DVB Cable Locator Information"),
        pointer_default(unique)
    ]
    interface IDVBCLocator : ILocator
    {
    };

//////////////////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////////////////
// TYPELIB & CoClasses
//////////////////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////////////////
[
    uuid(9B085638-018E-11d3-9D8E-00C04F72D980),
    version(1.0),
    helpstring("Microsoft Tuner 1.0 Type Library")
]
library TunerLib
{
    importlib("stdole2.tlb");

    // SystemTuningSpaces class
    [
        uuid(D02AAC50-027E-11d3-9D8E-00C04F72D980),
        helpstring("SystemTuningSpace Class")
    ]
    coclass SystemTuningSpaces
    {
        [default] interface ITuningSpaceContainer;
    };

	// NOTE: there is no object factory for this class.  the implementation for the underlying
	// code is a c++ abstract base class.  this coclass is only provided here to
	// force vb to expose the base interface to enable polymorphic access to 
	// derived objects
    [
		hidden, restricted,
        uuid(5FFDC5E6-B83A-4b55-B6E8-C69E765FE9DB),
        helpstring("dummy class to expose base tuning space i/f to VB")
    ]
    coclass TuningSpace
    {
        [default] interface ITuningSpace;
    };


    [
        uuid(A2E30750-6C3D-11d3-B653-00C04F79498E),
        helpstring("ATSC Digital Broadcast Tuning Space Class")
    ]
    coclass ATSCTuningSpace
    {
        [default] interface IATSCTuningSpace;
    };

    [
        uuid(8A674B4C-1F63-11d3-B64C-00C04F79498E),
        helpstring("Analog Radio Tuning Space Class")
    ]
    coclass AnalogRadioTuningSpace
    {
        [default] interface IAnalogRadioTuningSpace;
    };

    [
        uuid(8A674B4D-1F63-11d3-B64C-00C04F79498E),
        helpstring("Analog TV Tuning Space Class")
    ]
    coclass AnalogTVTuningSpace
    {
        [default] interface IAnalogTVTuningSpace;
    };

    [
        uuid(C6B14B32-76AA-4a86-A7AC-5C79AAF58DA7),
        helpstring("DVB Tuning Space Class")
    ]
    coclass DVBTuningSpace
    {
        [default] interface IDVBTuningSpace;
    };



    // ComponentTypes container class
    [
        uuid(A1A2B1C4-0E3A-11d3-9D8E-00C04F72D980),
        helpstring("Component Types Collection Class")
    ]
    coclass ComponentTypes
    {
        [default] interface IComponentTypes;
    };

    // ComponentType class
    [
        uuid(823535A0-0318-11d3-9D8E-00C04F72D980),
        helpstring("ComponentType Class")
    ]
    coclass ComponentType
    {
        [default] interface IComponentType;
    };

    // Language ComponentType class
    [
        uuid(1BE49F30-0E1B-11d3-9D8E-00C04F72D980),
        helpstring("LanguageComponentType Class")
    ]
    coclass LanguageComponentType
    {
        [default] interface ILanguageComponentType;
    };

    // MPEG2 ComponentType class
    [
        uuid(418008F3-CF67-4668-9628-10DC52BE1D08),
        helpstring("MPEG2ComponentType Class")
    ]
    coclass MPEG2ComponentType
    {
        [default] interface IMPEG2ComponentType;
    };

    // ATSC ComponentType class
    [
        uuid(A8DCF3D5-0780-4ef4-8A83-2CFFAACB8ACE),
        helpstring("ATSCComponentType Class")
    ]
    coclass ATSCComponentType
    {
        [default] interface IATSCComponentType;
    };

    // Components class
    [
        hidden, restricted,
        uuid(809B6661-94C4-49e6-B6EC-3F0F862215AA),
        helpstring("Components Collection Class")
    ]
    coclass Components
    {
        [default] interface IComponents;
    };

    // Component class
	// the components can only be created by network provider/tif.  client apps
	// retrieve them from a filled-in tune request by either loading a persisted tune
	// request that has been filled in by a proper guide store loader or by re-getting
	// the current tune request property which will have this filled in by the network
	// provider
    [
        hidden, restricted,
        uuid(59DC47A8-116C-11d3-9D8E-00C04F72D980),
        helpstring("Component Class")
    ]
    coclass Component
    {
        [default] interface IComponent;
    };

    // MPEG2 Component class
    [
        hidden, restricted,
        uuid(055CB2D7-2969-45cd-914B-76890722F112),
        helpstring("MPEG2 Component Class")
    ]
    coclass MPEG2Component
    {
        [default] interface IMPEG2Component;
    };


	// NOTE: there is no object factory for this class.  the implementation for the underlying
	// code is a c++ abstract base class.  this coclass is only provided here to
	// force vb to expose the base interface to enable polymorphic access to 
	// derived objects
    [
		hidden, restricted,
        uuid(B46E0D38-AB35-4a06-A137-70576B01B39F),
        helpstring("dummy class to expose base tune request i/f to VB")
    ]
    coclass TuneRequest
    {
        [default] interface ITuneRequest;
    };


    // NOTE: the tuning request factories should not be invoked directly.
    // instead apps should use ITuningSpace::CreateTuningRequest to obtain a correctly
    // initialized tuning request of the appropriate type for that space.
    // the object factories are provided to allow generic code to a load a previously persisted
    // tune request object.
    [
        hidden,
        restricted,
        uuid(0369B4E5-45B6-11d3-B650-00C04F79498E),
        helpstring("Channel Tune Request")
    ]
    coclass ChannelTuneRequest
    {
        [default] interface IChannelTuneRequest;
    };

    [
        hidden,
        restricted,
        uuid(0369B4E6-45B6-11d3-B650-00C04F79498E),
        helpstring("ATSC Channel Tune Request")
    ]
    coclass ATSCChannelTuneRequest
    {
        [default] interface IATSCChannelTuneRequest;
    };


	// NOTE: there is no object factory for this class.  the implementation for the underlying
	// code is a c++ abstract base class.  this coclass is only provided here to
	// force vb to expose the base interface to enable polymorphic access to 
	// derived objects
    [
		hidden, restricted,
        uuid(0888C883-AC4F-4943-B516-2C38D9B34562),
        helpstring("dummy class to expose base locator i/f to VB")
    ]
    coclass Locator
    {
        [default] interface ILocator;
    };

	// its not intended that client apps actually create locators.  the network
	// provider in combination with the transport information filter(TIF) are the 
	// only modules in the system that actually know the correct values for these objects
	// however, for testing purposes, its necessary to allow these to be created.
	// instead, normally these will simply be passed around opaquely since they're a 
	// property of the base tune request interface.
    [
        uuid(8872FF1B-98FA-4d7a-8D93-C9F1055F85BB),
        helpstring("ATSC Locator")
    ]
    coclass ATSCLocator
    {
        [default] interface IATSCLocator;
    };

    [
        uuid(9CD64701-BDF3-4d14-8E03-F12983D86664),
        helpstring("DVB-Terrestrial Locator")
    ]
    coclass DVBTLocator
    {
        [default] interface IDVBTLocator;
    };

    [
        uuid(1DF7D126-4050-47f0-A7CF-4C4CA9241333),
        helpstring("DVB-Satellite Locator")
    ]
    coclass DVBSLocator
    {
        [default] interface IDVBSLocator;
    };

    [
        uuid(C531D9FD-9685-4028-8B68-6E1232079F1E),
        helpstring("DVB-Cable Locator")
    ]
    coclass DVBCLocator
    {
        [default] interface ILocator;
    };

    [
        hidden,
        restricted,
        uuid(15D6504A-5494-499c-886C-973C9E53B9F1),
        helpstring("DVB Tune Request")
    ]
    coclass DVBTuneRequest
    {
        [default] interface IDVBTuneRequest;
    };

// Sample tuner class definition:
//
//  [
//      uuid(<some guid>),
//      helpstring("My Tuner object")
//  ]
//  coclass MyTuner
//  {
//      [default] interface ITuner;
//      interface IMyTunerCustomFeatureInterface;
//      [default, source] interface ITunerEvents;
//      [source] interface ISignalEvents;
//  };

///////////////////////////////////////////////////////////////////////////////////////
// utility objects
///////////////////////////////////////////////////////////////////////////////////////
    [
		hidden, restricted,
        uuid(8A674B49-1F63-11d3-B64C-00C04F79498E),
        helpstring("Create property bag backed by registry")
    ]
    coclass CreatePropBagOnRegKey
    {
        interface ICreatePropBagOnRegKey;
    };

}

// end of file -- tuner.idl
