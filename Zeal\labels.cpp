#include "labels.h"
#include "EqStructures.h"
#include "EqAddresses.h"
#include "EqFunctions.h"
#include "EqUI.h"
#include "Zeal.h"
#include <cmath>
#include <algorithm>

void default_empty(Zeal::EqUI::CXSTR* str, bool* override_color, ULONG* color)
{
	*override_color = 1;
	*color = 0xffc0c0c0;
	Zeal::EqGame::CXStr_PrintString(str, "");
}


bool GetLabelFromEq(int EqType, Zeal::EqUI::CXSTR* str, bool* override_color, ULONG* color)
{
	ZealService* zeal = ZealService::get_instance();
	if (!Zeal::EqGame::is_in_game())
		return ZealService::get_instance()->hooks->hook_map["GetLabel"]->original(GetLabelFromEq)(EqType, str, override_color, color);
	switch (EqType)
	{
	case 80:
	{
		if (!Zeal::EqGame::get_char_info())
			return true;
		int max_mana = Zeal::EqGame::get_char_info()->max_mana();//  Zeal::EqGame::EqGameInternal::get_max_mana(*Zeal::EqGame::ptr_LocalPC, 0);
		int mana = Zeal::EqGame::get_char_info()->mana(); //Zeal::EqGame::EqGameInternal::get_cur_mana(*Zeal::EqGame::ptr_LocalPC, 0);

		// Update mana tracking for time-to-full calculation
		zeal->labels_hook->update_mana_tracking(mana);

		// Calculate time to full mana
		int minutes_to_full = zeal->labels_hook->calculate_minutes_to_full(mana, max_mana);

		// Format: current/max on left, time estimate on right
		if (minutes_to_full > 0 && mana < max_mana) {
			Zeal::EqGame::CXStr_PrintString(str, "%d/%d    %dm", mana, max_mana, minutes_to_full);
		} else {
			Zeal::EqGame::CXStr_PrintString(str, "%d/%d", mana, max_mana);
		}
		*override_color = false;
		return true;
	}
	case 81:
	{
		if (!zeal->experience)
			return true;
		Zeal::EqGame::CXStr_PrintString(str, "%.f", zeal->experience->exp_per_hour_pct_tot);
		*override_color = false;
		return true;
	}
	case 82:
	{
		Zeal::EqStructures::Entity* target = Zeal::EqGame::get_target();
		if (target && target->PetOwnerSpawnId > 0)
		{
			Zeal::EqStructures::Entity* owner = Zeal::EqGame::get_entity_by_id(target->PetOwnerSpawnId);
			if (owner)
			{
				Zeal::EqGame::CXStr_PrintString(str, "%s", owner->Name);
				*override_color = false;
			}
		}
		else
		{
			default_empty(str, override_color, color);
		}
		return true;
	}
	case 83:  // Number of empty inventory slots.
	{
		int num_empty = Zeal::EqGame::get_num_empty_inventory_slots();
		Zeal::EqGame::CXStr_PrintString(str, "%d", num_empty);
		*override_color = true;
		*color = (num_empty <= 0) ? 0xffff0000 : ((num_empty == 1) ? 0xffffff00 : 0xffc0c0c0);
		return true;
	}
	case 84:  // Total number of inventory slots.
		Zeal::EqGame::CXStr_PrintString(str, "%d", Zeal::EqGame::get_num_inventory_slots());
		*override_color = false;
		return true;
	case 85:  // Number of filled inventory slots.
	{
		int total_num = Zeal::EqGame::get_num_inventory_slots();
		int num_empty = Zeal::EqGame::get_num_empty_inventory_slots();
		Zeal::EqGame::CXStr_PrintString(str, "%d", total_num - num_empty);
		*override_color = true;
		*color = (num_empty <= 0) ? 0xffff0000 : ((num_empty == 1) ? 0xffffff00 : 0xffc0c0c0);
		return true;
	}
	case 124:
	{
		if (Zeal::EqGame::get_char_info())
			Zeal::EqGame::CXStr_PrintString(str, "%d", Zeal::EqGame::get_char_info()->mana());
		*override_color = false;
		return true;
	}
	case 125:
	{
		if (Zeal::EqGame::get_char_info())
			Zeal::EqGame::CXStr_PrintString(str, "%d", Zeal::EqGame::get_char_info()->max_mana());
		*override_color = false;
		return true;
	}
	case 134:
	{
		if (Zeal::EqGame::get_controlled() && Zeal::EqGame::get_controlled()->ActorInfo)
		{
			if (Zeal::EqGame::get_controlled()->ActorInfo->CastingSpellId) {
				int spell_id = Zeal::EqGame::get_controlled()->ActorInfo->CastingSpellId;
				if (spell_id == kInvalidSpellId) spell_id = 0; // avoid crash while player is not casting a spell
				Zeal::EqStructures::SPELL* casting_spell = Zeal::EqGame::get_spell_mgr()->Spells[spell_id];
				Zeal::EqGame::CXStr_PrintString(str, "%s", casting_spell->Name);
				*override_color = false;
			}

		}
		return true;
	}
	case 135: // Buff 16
	case 136: // Buff 17
	case 137: // Buff 18
	case 138: // Buff 19
	case 139: // Buff 20
	case 140: // Buff 21
	case 141: // Buff 22
	case 142: // Buff 23
	case 143: // Buff 24
	case 144: // Buff 25
	case 145: // Buff 26
	case 146: // Buff 27
	case 147: // Buff 28
	case 148: // Buff 29
	case 149: // Buff 30
		break; // Reserved - These labels are supported by the eqgame.dll
	case 255: //debug label
	{
		Zeal::EqGame::CXStr_PrintString(str, "%s", ZealService::get_instance()->labels_hook->debug_info.c_str());
		ZealService::get_instance()->labels_hook->debug_info = "";
		*override_color = false;
		return true;
	}
	default:
		break;
	}
	return ZealService::get_instance()->hooks->hook_map["GetLabel"]->original(GetLabelFromEq)(EqType, str, override_color, color);
}

static int get_remaining_cast_recovery_time()
{
	auto self = Zeal::EqGame::get_self();
	auto actor_info = self ? self->ActorInfo : nullptr;
	int* this_display = *(int**)Zeal::EqGame::Display;
	if (!self || !actor_info || !this_display)
		return 0;

	int game_time = this_display[200 / 4];  // The client uses this display offset as a timestamp.
	if (actor_info->FizzleTimeout <= game_time)
		return 0;  // Idle state.

	int time_left = max(0, actor_info->FizzleTimeout - game_time);
	return time_left;
}

static int get_attack_timer_gauge(Zeal::EqUI::CXSTR* str)
{
	// Logic for the attack recovery timer was copied from DoProcessTime() which sets 0x007cd844.
	auto self = Zeal::EqGame::get_self();
	auto actor_info = self ? self->ActorInfo : nullptr;
	auto char_info = Zeal::EqGame::get_char_info();
	bool ready_to_attack = *reinterpret_cast<bool*>(0x007cd844);  // 0 = attacking, 1 = ready.
	if (!self || !actor_info || !char_info || ready_to_attack) {
		if (str)
			str->Set("");
		return 0;
	}

	UINT range_delay = 0;
	bool is_bow = false;
	if (actor_info->LastAttack == 11) {  // Ranged.
		// Calculate ranged time.
		auto range_item = char_info->InventoryItem[10];  // Ranged slot.
		if (range_item && range_item->Common.AttackDelay) {
			if (range_item->Common.Skill < 6 || range_item->Common.Skill == 0xd)
				range_delay = range_item->Common.AttackDelay * 100;
			else if (range_item->Common.Skill == 0x16)
				range_delay = reinterpret_cast<UINT**>(0x007f7aec)[range_item->Common.AttackDelay][1];
			is_bow = range_delay && (range_item->Common.Skill == 5);
		}
	}

	UINT attack_delay = range_delay;  // Use range_delay if it was set non-zero above.
	if (attack_delay == 0) {
		auto primary_item = char_info->InventoryItem[12];  // Primary slot.
		if (!primary_item || !primary_item->Common.AttackDelay)  // No weapon or not a weapon.
			attack_delay = Zeal::EqGame::get_hand_to_hand_delay() * 100;
		else if (primary_item->Common.Skill < 6 || primary_item->Common.Skill == 0x2d)  // Uses patched 0x2d, not 0xd.
			attack_delay = primary_item->Common.AttackDelay * 100;
		else if (primary_item->Common.Skill == 0x16)  // Hand-to-hand skilldict lookup.
			attack_delay = reinterpret_cast<UINT**>(0x007f7aec)[primary_item->Common.AttackDelay][1];
	}

	if (attack_delay)
		attack_delay = self->ModifyAttackSpeed(attack_delay, is_bow);

	UINT delay_time = Zeal::EqGame::get_eq_time() - actor_info->AttackTimer;
	if (attack_delay == 0 || attack_delay <= delay_time) {
		if (str)
			str->Set("");
		return 0;
	}

	int time_left = attack_delay - delay_time;
	if (str) {
		int secs_left = (time_left + 999) / 1000;  // Show 3, 2, 1, 0 countdown effectively.
		Zeal::EqGame::CXStr_PrintString(str, "%i", secs_left);
	}

	const int full_duration = attack_delay;  // Use 4 seconds as the normalization factor.
	return max(0, min(1000, 1000 * time_left / attack_delay));
}

static int get_recast_time_gauge(int index, Zeal::EqUI::CXSTR* str)
{
	bool invalid_index = index < 0 || index >= EQ_NUM_SPELL_GEMS;

	auto self = Zeal::EqGame::get_self();
	auto actor_info = self ? self->ActorInfo : nullptr;
	auto char_info = Zeal::EqGame::get_char_info();
	int* this_display = *(int**)Zeal::EqGame::Display;
	if (invalid_index || !self || !actor_info || !char_info || !this_display) {
		if (str)
			str->Set("0");
		return 0;
	}

	// Empty gauge if recast timeout is < current game time or the fizzle timeout (GCD).
	int game_time = this_display[200 / 4];  // The client uses this display offset as a timestamp.
	int spell_id = char_info->MemorizedSpell[index];
	if (!Zeal::EqGame::Spells::IsValidSpellIndex(spell_id) ||
		actor_info->CastingSpellId == spell_id ||
		actor_info->RecastTimeout[index] <= game_time ||
		actor_info->RecastTimeout[index] <= actor_info->FizzleTimeout) {
		if (str)
			str->Set("0");
		return 0;
	}

	int time_left = actor_info->RecastTimeout[index] - game_time;
	if (str) {
		int secs_left = (time_left + 500) / 1000;
		Zeal::EqGame::CXStr_PrintString(str, "%i", secs_left);
	}

	auto sp_mgr = Zeal::EqGame::get_spell_mgr();
	int full_duration = sp_mgr ? sp_mgr->Spells[spell_id]->RecastTime : 0;
	full_duration = max(1000, full_duration);  // Ensure non-zero and reasonable number.
	return max(0, min(1000, 1000 * time_left / full_duration));
}

int GetGaugeFromEq(int EqType, Zeal::EqUI::CXSTR* str)
{
	ZealService* zeal = ZealService::get_instance();
	switch (EqType)
	{
		case 23:
		{
			if (!zeal->experience) //possible nullptr crash (race condition)
				return 0;
			float fpct = zeal->experience->exp_per_hour_pct_tot / 100.f;
			return (int)(1000.f * fpct);
		}
		case 24: // Server Tick
		{
			if (zeal->tick)
				return zeal->tick->GetTickGauge(str);
			return 0;
		}
		case 25:  // Global cast recovery gauge.
		{
			static constexpr int kNominalMaxRecoveryTime = 2500; // In milliseconds.
			int recovery_time = get_remaining_cast_recovery_time();
			int value = max(0, min(1000, recovery_time * 1000 / kNominalMaxRecoveryTime));
			Zeal::EqGame::CXStr_PrintString(str, "%i", (recovery_time + 500) / 1000);
			return value;
		}
		case 26:  // Spell0 recast time.
		case 27:  // Spell1 recast time.
		case 28:  // Spell2 recast time.
		case 29:  // Spell3 recast time.
		case 30:  // Spell4 recast time.
		case 31:  // Spell5 recast time.
		case 32:  // Spell6 recast time.
		case 33:  // Spell7 recast time.
			return get_recast_time_gauge(EqType - 26, str);
		case 34:  // Attack recovery timer.
			return get_attack_timer_gauge(str);
		case 35:  // Mana regeneration timer.
		{
			if (!Zeal::EqGame::get_char_info())
				return 0;

			int max_mana = Zeal::EqGame::get_char_info()->max_mana();
			int mana = Zeal::EqGame::get_char_info()->mana();

			// Update mana tracking for time-to-full calculation
			zeal->labels_hook->update_mana_tracking(mana);

			// Calculate time to full mana
			int minutes_to_full = zeal->labels_hook->calculate_minutes_to_full(mana, max_mana);

			// Display the timer text
			if (minutes_to_full > 0 && mana < max_mana) {
				Zeal::EqGame::CXStr_PrintString(str, "%d/%d %dm", mana, max_mana, minutes_to_full);
			} else {
				Zeal::EqGame::CXStr_PrintString(str, "%d/%d", mana, max_mana);
			}

			// Return gauge value (mana percentage for the bar)
			return (max_mana > 0) ? (mana * 1000 / max_mana) : 0;
		}
		default:
			break;
	}

	int result = ZealService::get_instance()->hooks->hook_map["GetGauge"]->original(GetGaugeFromEq)(EqType, str);

	switch (EqType)
	{
		case 11:  // Intercept the player HP gauges (group window typically) to tag the leader.
		case 12:
		case 13:
		case 14:
		case 15:
		{
			const Zeal::EqStructures::GroupInfo* group_info = Zeal::EqGame::GroupInfo;
			if (group_info->is_in_group() && strcmp(str->CastToCharPtr(), group_info->LeaderName) == 0)
			{
				std::string name = std::string(*str) + "*";
				str->Set(name.c_str());
			}
		}
		break;
		default:
			break;
	}
	return result;
}

void Labels::print_debug_info(std::string data)
{
	debug_info = data;
}
void Labels::print_debug_info(const char* format, ...)
{
	va_list argptr;
	char buffer[512];
	va_start(argptr, format);
	//printf()
	vsnprintf(buffer, 511, format, argptr);
	va_end(argptr);
	if (debug_info.length()>0)
		debug_info += "\n" + std::string(buffer);
	else
		debug_info += std::string(buffer);
}


void Labels::callback_main()
{


}

bool Labels::GetLabel(int EqType, std::string& str)
{
	Zeal::EqUI::CXSTR tmp("");
	bool override = false;
	ULONG color = 0;
	bool val = GetLabelFromEq(EqType, (Zeal::EqUI::CXSTR*)&tmp, &override, &color);
	if (tmp.Data)
	{
		str = std::string(tmp);
		tmp.FreeRep();
	}
	return val;
}
int Labels::GetGauge(int EqType, std::string& str)
{
	Zeal::EqUI::CXSTR tmp("");
	int value = GetGaugeFromEq(EqType, (Zeal::EqUI::CXSTR*)&tmp);
	if (tmp.Data)
	{
		str = std::string(tmp);
		tmp.FreeRep();
	}
	return value;
}


Labels::~Labels()
{

}

void Labels::update_mana_tracking(int current_mana)
{
	auto now = std::chrono::steady_clock::now();

	// Initialize if this is the first check
	if (last_mana == -1) {
		last_mana = current_mana;
		last_mana_check = now;
		return;
	}

	// Check if mana increased (uptick) and enough time has passed
	auto time_diff = std::chrono::duration_cast<std::chrono::seconds>(now - last_mana_check);
	if (current_mana > last_mana && time_diff.count() >= 1) { // At least 1 second between checks
		int mana_gained = current_mana - last_mana;

		// Record this uptick
		last_uptick = {now, mana_gained, current_mana};

		last_mana = current_mana;
		last_mana_check = now;
	}
	// Update last_mana even on downticks, but don't record them
	else if (current_mana != last_mana) {
		last_mana = current_mana;
		last_mana_check = now;
	}
}

int Labels::calculate_minutes_to_full(int current_mana, int max_mana)
{
	if (current_mana >= max_mana) {
		return 0;
	}

	// Check if we have a valid last uptick and it's not too old (within 5 minutes)
	auto now = std::chrono::steady_clock::now();
	auto cutoff_time = now - std::chrono::minutes(5);
	if (last_uptick.timestamp < cutoff_time || last_uptick.mana_gained <= 0) {
		return 0;
	}

	// Calculate time since last uptick
	auto time_since_uptick = std::chrono::duration_cast<std::chrono::milliseconds>(
		now - last_uptick.timestamp);
	double seconds_since_uptick = time_since_uptick.count() / 1000.0;

	// Estimate current mana regeneration rate based on the last uptick
	// We assume the uptick represents the mana gained over some period
	// For simplicity, we'll use the mana gained from the last uptick as the rate per tick
	double mana_per_uptick = static_cast<double>(last_uptick.mana_gained);

	// Estimate uptick interval (assume standard EQ tick of ~6 seconds if we don't have better data)
	double estimated_uptick_interval = 6.0; // seconds

	double mana_per_second = mana_per_uptick / estimated_uptick_interval;
	if (mana_per_second <= 0) {
		return 0;
	}

	int mana_needed = max_mana - current_mana;
	double seconds_to_full = mana_needed / mana_per_second;
	int minutes_to_full = static_cast<int>(std::ceil(seconds_to_full / 60.0));

	// Cap at reasonable maximum (999 minutes)
	return (minutes_to_full < 999) ? minutes_to_full : 999;
}

Labels::Labels(ZealService* zeal)
{
	zeal->commands_hook->Add("/labels", {}, "prints all labels",
		[this](std::vector<std::string>& args) {
			for (int i = 0; i < 200; i++)
			{
				Zeal::EqUI::CXSTR tmp("");
				bool override = false;
				ULONG color = 0;
				GetLabelFromEq(i, (Zeal::EqUI::CXSTR*)&tmp, &override, &color);
				if (tmp.Data) {
					Zeal::EqGame::print_chat("label: %i value: %s", i, tmp.CastToCharPtr());
					tmp.FreeRep();
				}
			}
			return true; //return true to stop the game from processing any further on this command, false if you want to just add features to an existing cmd
		});
	// zeal->callbacks->add_generic([this]() { callback_main(); }); //causes a crash because callback_main is empty
	//zeal->hooks->Add("FinalizeLoot", Zeal::EqGame::EqGameInternal::fn_finalizeloot, finalize_loot, hook_type_detour);
	zeal->hooks->Add("GetLabel", Zeal::EqGame::EqGameInternal::fn_GetLabelFromEQ, GetLabelFromEq, hook_type_detour);
	zeal->hooks->Add("GetGauge", Zeal::EqGame::EqGameInternal::fn_GetGaugeLabelFromEQ, GetGaugeFromEq, hook_type_detour);
}
