#!/usr/bin/env python3
"""
Test script for the EverQuest memory reader
-------------------------------------------
This script tests the memory reading functions to ensure they work correctly.
"""

import sys
import time
from eq_map_tracker import MemoryReader, ENTITY_TYPE_PLAYER, ENTITY_TYPE_NPC

def main():
    """Main test function"""
    print("EverQuest Memory Reader Test")
    print("----------------------------")
    
    # Create memory reader
    reader = MemoryReader()
    
    # Connect to EverQuest
    print("Connecting to EverQuest...")
    if not reader.connect_to_eq():
        print("Failed to connect to EverQuest. Make sure the game is running.")
        return
    
    print("Connected to EverQuest!")
    
    # Test zone info
    print("\nTesting zone info...")
    zone = reader.get_current_zone()
    print(f"Current zone: {zone}")
    
    # Test player position
    print("\nTesting player position...")
    position = reader.get_player_position()
    print(f"Player position: X={position.x}, Y={position.y}, Z={position.z}")
    
    # Test entity list
    print("\nTesting entity list...")
    entities = reader.get_entities()
    
    if not entities:
        print("No entities found.")
    else:
        print(f"Found {len(entities)} entities.")
        
        # Count entity types
        player_count = sum(1 for e in entities if e.type == 'player')
        npc_count = sum(1 for e in entities if e.type == 'npc')
        npc_corpse_count = sum(1 for e in entities if e.type == 'npc_corpse')
        player_corpse_count = sum(1 for e in entities if e.type == 'player_corpse')
        
        print(f"Players: {player_count}")
        print(f"NPCs: {npc_count}")
        print(f"NPC Corpses: {npc_corpse_count}")
        print(f"Player Corpses: {player_corpse_count}")
        
        # Print some entity details
        print("\nSample entities:")
        for i, entity in enumerate(entities[:10]):  # Show first 10 entities
            print(f"{i+1}. {entity.name} (ID: {entity.id}, Level: {entity.level}, Type: {entity.type})")
            print(f"   Position: X={entity.position.x}, Y={entity.position.y}, Z={entity.position.z}")
    
    # Close the connection
    reader.close()
    print("\nTest completed.")

if __name__ == "__main__":
    main()
