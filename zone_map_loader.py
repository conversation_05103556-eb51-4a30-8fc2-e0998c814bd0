#!/usr/bin/env python3
"""
Zone Map Loader
--------------
Loads and processes EverQuest zone map files from the Zeal codebase.
"""

import os
import glob
import math
from dataclasses import dataclass
from typing import List, Dict, Tuple, Optional

@dataclass
class MapLine:
    """A line segment in the zone map"""
    x0: float
    y0: float
    z0: float
    x1: float
    y1: float
    z1: float
    red: int
    green: int
    blue: int
    level_id: int = 0  # Default to main level (0)

@dataclass
class MapLabel:
    """A text label in the zone map"""
    x: float
    y: float
    z: float
    red: int
    green: int
    blue: int
    text: str

@dataclass
class MapLevel:
    """Information about a level in the zone map"""
    level_id: int
    max_z: float
    min_z: float

@dataclass
class ZoneMap:
    """A complete zone map with lines, labels, and level information"""
    name: str
    max_x: float
    min_x: float
    max_y: float
    min_y: float
    max_z: float
    min_z: float
    lines: List[MapLine]
    labels: List[MapLabel]
    levels: Dict[int, MapLevel]

class ZoneMapLoader:
    """Class to load and process EverQuest zone map files"""
    
    def __init__(self, map_dir: str = "Zeal/zone_map_src/map_files"):
        """Initialize the loader with the directory containing map files"""
        self.map_dir = map_dir
        self.zone_maps: Dict[str, ZoneMap] = {}
        self.zone_name_to_id: Dict[str, int] = {}
        self.zone_id_to_name: Dict[int, str] = {}
        
        # Load the zone ID lookup table if available
        zone_id_lut_path = os.path.join(os.path.dirname(map_dir), "zone_id_lut.csv")
        if os.path.exists(zone_id_lut_path):
            self._load_zone_id_lut(zone_id_lut_path)
    
    def _load_zone_id_lut(self, path: str):
        """Load the zone ID lookup table"""
        try:
            with open(path, 'r') as f:
                for line in f:
                    parts = line.strip().split(',')
                    if len(parts) >= 2:
                        zone_id = int(parts[0])
                        zone_name = parts[1].lower()
                        self.zone_name_to_id[zone_name] = zone_id
                        self.zone_id_to_name[zone_id] = zone_name
        except Exception as e:
            print(f"Error loading zone ID lookup table: {e}")
    
    def load_zone_map(self, zone_name: str) -> Optional[ZoneMap]:
        """Load a zone map by name"""
        # Check if we already loaded this map
        if zone_name in self.zone_maps:
            return self.zone_maps[zone_name]
        
        # Find all map files for this zone
        zone_files = glob.glob(os.path.join(self.map_dir, f"{zone_name}*.txt"))
        if not zone_files:
            print(f"No map files found for zone: {zone_name}")
            return None
        
        # Parse all map files for this zone
        all_lines = []
        all_labels = []
        
        for file_path in zone_files:
            lines, labels = self._parse_map_file(file_path)
            all_lines.extend(lines)
            all_labels.extend(labels)
        
        if not all_lines:
            print(f"No lines found in map files for zone: {zone_name}")
            return None
        
        # Calculate map boundaries
        min_x = min(min(line.x0, line.x1) for line in all_lines)
        max_x = max(max(line.x0, line.x1) for line in all_lines)
        min_y = min(min(line.y0, line.y1) for line in all_lines)
        max_y = max(max(line.y0, line.y1) for line in all_lines)
        min_z = min(min(line.z0, line.z1) for line in all_lines)
        max_z = max(max(line.z0, line.z1) for line in all_lines)
        
        # Ensure we have valid boundaries
        if max_x == min_x:
            max_x = min_x + 1
        if max_y == min_y:
            max_y = min_y + 1
        if max_z == min_z:
            max_z = min_z + 1
        
        # Calculate level information
        levels = self._calculate_levels(all_lines)
        
        # Create the zone map
        zone_map = ZoneMap(
            name=zone_name,
            max_x=max_x,
            min_x=min_x,
            max_y=max_y,
            min_y=min_y,
            max_z=max_z,
            min_z=min_z,
            lines=all_lines,
            labels=all_labels,
            levels=levels
        )
        
        # Cache the map for future use
        self.zone_maps[zone_name] = zone_map
        return zone_map
    
    def _parse_map_file(self, file_path: str) -> Tuple[List[MapLine], List[MapLabel]]:
        """Parse a map file and extract lines and labels"""
        lines = []
        labels = []
        
        try:
            with open(file_path, 'r') as f:
                for line in f:
                    line = line.strip()
                    if not line:
                        continue
                    
                    parts = line[1:].split(',')
                    
                    if line[0] == 'L':  # Line
                        if len(parts) >= 9:
                            lines.append(MapLine(
                                x0=float(parts[0]),
                                y0=float(parts[1]),
                                z0=float(parts[2]),
                                x1=float(parts[3]),
                                y1=float(parts[4]),
                                z1=float(parts[5]),
                                red=int(parts[6]),
                                green=int(parts[7]),
                                blue=int(parts[8]),
                                level_id=self._get_level_id_from_color(int(parts[6]), int(parts[7]), int(parts[8]))
                            ))
                    elif line[0] == 'P':  # Point/Label
                        if len(parts) >= 8:
                            text = parts[7].strip()
                            if text not in ['Succor', 'succor']:  # Skip Succor points
                                labels.append(MapLabel(
                                    x=float(parts[0]),
                                    y=float(parts[1]),
                                    z=float(parts[2]),
                                    red=int(parts[3]),
                                    green=int(parts[4]),
                                    blue=int(parts[5]),
                                    text=text
                                ))
        except Exception as e:
            print(f"Error parsing map file {file_path}: {e}")
        
        return lines, labels
    
    def _get_level_id_from_color(self, red: int, green: int, blue: int) -> int:
        """Determine the level ID based on the line color"""
        # Brewal maps color coding: https://www.eqmaps.info/eq-map-files/mapping-standards/
        level_lut = {
            (255, 168, 102): 6,  # Layer 2: Highest z (top)
            (204, 102, 0): 5,    # Layer 3
            (102, 255, 102): 4,  # Layer 4
            (0, 204, 0): 3,      # Layer 5
            (102, 255, 255): 2,  # Layer 6
            (0, 204, 204): 1,    # Layer 7
            (0, 0, 0): 0,        # Layer main
            (255, 102, 255): -1, # Layer 8
            (204, 0, 204): -2,   # Layer 9
            (178, 102, 255): -3, # Layer 10
            (102, 0, 204): -4,   # Layer 11: Most negative z (bottom)
        }
        
        return level_lut.get((red, green, blue), 0)  # Default to main level (0)
    
    def _calculate_levels(self, lines: List[MapLine]) -> Dict[int, MapLevel]:
        """Calculate level information from the lines"""
        level_heights = {}
        
        # Find all unique level IDs
        level_ids = set(line.level_id for line in lines)
        
        # Calculate min and max Z for each level
        for level_id in level_ids:
            level_lines = [line for line in lines if line.level_id == level_id]
            if level_lines:
                min_z = min(min(line.z0, line.z1) for line in level_lines)
                max_z = max(max(line.z0, line.z1) for line in level_lines)
                level_heights[level_id] = MapLevel(level_id=level_id, min_z=min_z, max_z=max_z)
        
        return level_heights
    
    def get_zone_id(self, zone_name: str) -> Optional[int]:
        """Get the zone ID for a zone name"""
        return self.zone_name_to_id.get(zone_name.lower())
    
    def get_zone_name(self, zone_id: int) -> Optional[str]:
        """Get the zone name for a zone ID"""
        return self.zone_id_to_name.get(zone_id)
