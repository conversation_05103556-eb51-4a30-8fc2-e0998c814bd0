//------------------------------------------------------------------------------
// File: AMVPE.idl
//
// Desc: 
//
// Copyright (c) 1992 - 2000, Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------------------------


import "unknwn.idl";
import "objidl.idl";	// for IPersist (<PERSON><PERSON><PERSON>'s root)


/*
 * VIDOESIGNALINFO
 */
typedef struct _VIDEOSIGNALINFO
{
    DWORD dwSize;				// Size of the structure
    DWORD dwVREFHeight;			// Specifies the number of lines of data in the vref
	BOOL  bDoubleClock;			// videoport should enable double clocking
	<PERSON><PERSON><PERSON>  bVACT;				// videoport should use an external VACT signal
	BOOL  bInterlaced;			// Indicates that the signal is interlaced
	BOOL  bHalfline;			// <PERSON><PERSON> will write half lines into the frame buffer
	BOOL  bInvertedPolarity;    // Devoce inverts the polarity by default
} AMVIDEOSIGNALINFO;

typedef struct AMVIDEOSIGNALINFO        *LPAMVIDEOSIGNALINFO;


interface IVPEConfig;
interface IVPE;


/*
 * DDVIDEOPORTCONNECT
 */
typedef struct _DDVIDEOPORTCONNECT
{
    DWORD dwSize;			// size of the DDVIDEOPORTCONNECT structure
    GUID  guidTypeID; 			// Description of video port connection
    DWORD dwPortWidth;			// Width of the video port
    DWORD dwFlags;			// Connection flags
} DDVIDEOPORTCONNECT;

typedef DDVIDEOPORTCONNECT *LPDDVIDEOPORTCONNECT;



/*
 * DDPIXELFORMAT
 */
typedef struct _DDPIXELFORMAT
{
    DWORD	dwSize;			// size of structure
    DWORD	dwFlags;		// pixel format flags
    DWORD	dwFourCC;		// (FOURCC code)
    union
    {
	DWORD	dwRGBBitCount;		// how many bits per pixel (BD_1,2,4,8,16,24,32)
	DWORD	dwYUVBitCount;		// how many bits per pixel (BD_4,8,16,24,32)
	DWORD	dwZBufferBitDepth;	// how many bits for z buffers (BD_8,16,24,32)
	DWORD	dwAlphaBitDepth;	// how many bits for alpha channels (BD_1,2,4,8)
    };
    union
    {
	DWORD	dwRBitMask;		// mask for red bit
	DWORD	dwYBitMask;		// mask for Y bits
    };
    union
    {
	DWORD	dwGBitMask;		// mask for green bits
	DWORD	dwUBitMask;		// mask for U bits
    };
    union
    {
	DWORD	dwBBitMask;		// mask for blue bits
	DWORD	dwVBitMask;		// mask for V bits
    };
    union
    {
	DWORD	dwRGBAlphaBitMask;	// mask for alpha channel
	DWORD	dwYUVAlphaBitMask;	// mask for alpha channel
	DWORD	dwRGBZBitMask;		// mask for Z channel
	DWORD	dwYUVZBitMask;		// mask for Z channel
    };
} DDPIXELFORMAT;

typedef DDPIXELFORMAT * LPDDPIXELFORMAT;


[
object,
uuid(BC29A660-30E3-11d0-9E69-00C04FD7C15B),
pointer_default(unique)
]

interface IVPEConfig : IUnknown {

		// gets the various connection information structures (guid, portwidth)
		// in an array of structures. If the pointer to the array is NULL, first 
		// parameter returns the total number of formats supported.
		HRESULT GetConnectInfo(
						[in,out] LPDWORD lpNumConnectInfo,
						[out] LPDDVIDEOPORTCONNECT lpddvpConnectInfo
		);

		HRESULT SetConnectInfo(
						[in] DDVIDEOPORTCONNECT ddvpConnectInfo
		);

		// gets the various formats supported by the decoder in an array
		// of structures. If the pointer to the array is NULL, first parameter
		// returns the total number of formats supported.
		HRESULT GetVideoFormats(
						[in,out] LPDWORD lpNumFormats,
						[out] LPDDPIXELFORMAT lpddpfFormats
		);

		// retrives maximum pixels per second rate expected for a given 
		// format and a given scaling factor. If decoder does not support 
		// those scaling factors, then it gives the rate and the nearest 
		// scaling factors.
		HRESULT GetMaxPixelRate(
						[in] DDPIXELFORMAT ddpfFormat,
						[in,out] LPDWORD lpdwZoomHeight,
						[in,out] LPDWORD lpdwZoomWidth,
						[out] LPDWORD lpdwMaxPixelsPerSecond
		);

		// retrives various properties of the decoder for a given format
		HRESULT GetVideoSignalInfo(
						[in] DDPIXELFORMAT ddpfFormat,
						[out] LPAMVIDEOSIGNALINFO lpAMVideoSignalInfo
		);
		
		// asks the decoder to ouput in this format. Return value should give
		// appropriate error code
		HRESULT SetVideoFormat(
						[in] DDPIXELFORMAT ddpfFormat
		);

		// asks the decoder to treat even fields like odd fields and visa versa
		HRESULT SetInvertPolarity(
		);

		// sets the scaling factors. If decoder does not support these,
		// then it sets the values to the nearest factors it can support
		HRESULT SetScalingFactors(
						[in,out] LPDWORD lpdwZoomHeight,
						[in,out] LPDWORD lpdwZoomWidth
		);
 
 }



[
object,
uuid(BC29A661-30E3-11d0-9E69-00C04FD7C15B),
pointer_default(unique)
]

interface IVPE : IUnknown {
		HRESULT SetOverlaySurface(
//                            [in] LPDIRECTDRAWSURFACE lpOverlaySurface,
                            [in] LPUNKNOWN lpOverlaySurface,
							[in] INT iNumBackBuffers
		);

}
