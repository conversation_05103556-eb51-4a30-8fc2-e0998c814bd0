P 223.6938, -14.8582, -40.4870, 255, 0, 0, 3, from_The_Plane_of_Tranquility
P -1663.5362, 866.4178, -294.8284, 255, 0, 0, 3, to_The_Plane_of_Tranquility
P -1031.1153, -1873.0318, -330.4628, 255, 0, 0, 3, to_The_Plane_of_War
P 940.1465, -15.2212, -238.4928, 255, 0, 0, 3, to_The_Plane_of_Tranquility
P 210.0000, -10.0000, -37.1875, 255, 0, 0, 2, Succor
P 834.2657, 1970.3084, 203.5242, 240, 240, 0, 2, to_Entrance_(click_door)
P 831.7332, -1979.6058, 202.0017, 240, 240, 0, 2, to_Entrance_(click_door)
P -279.0000, -182.0000, -0.3125, 240, 240, 0, 2, from_Teleporters
P 333.9570, -1418.9213, 110.0018, 0, 0, 0, 2, Keeper_of_Future_Lore
P -1230.9228, -1382.8714, -331.9975, 240, 240, 240, 2, <PERSON>ory
P -1487.0881, -507.8467, -309.9976, 240, 240, 240, 2, Barracks
<PERSON> -1265.5567, -2065.8293, -330.2979, 0, 0, 0, 2, <PERSON><PERSON>k
<PERSON> 174.7244, -49.5069, -41.9978, 0, 0, 0, 2, <PERSON>i<PERSON>_<PERSON><PERSON>
P -1416.1867, -1781.5728, -331.9976, 0, 0, 0, 2, <PERSON><PERSON>_<PERSON><PERSON>
P -1353.6987, -1737.9129, -331.9976, 0, 0, 0, 2, Myai_X`Lotl
P -1314.6026, -1744.5019, -331.9976, 0, 0, 0, 2, Martok_Weslent
P -1296.6833, -1759.7072, -331.9976, 0, 0, 0, 2, Rynd_Whistlesong
P -1201.0699, -1775.2580, -331.9975, 0, 0, 0, 2, Iani_Darkmoore
P -1183.4161, -1773.0068, -331.9976, 0, 0, 0, 2, Leara_Talissi
P -1151.3808, -1814.4273, -331.9976, 0, 0, 0, 2, Frana_Shimi
P -1175.7774, -1921.2541, -331.9976, 0, 0, 0, 2, Kaya_Novashine
P -1129.4063, -1913.2813, -331.9976, 0, 0, 0, 2, Aukris_Tomb
P -1171.6767, -1942.7540, -331.9976, 0, 0, 0, 2, Darrain_Lanseb
P -1734.5517, -1739.1004, -331.9976, 0, 0, 0, 2, Bouncer_Dunzuk
P -1723.3211, -1768.6629, -331.9976, 0, 0, 0, 2, Lysette_Wolftrot
P -1694.6296, -1922.0378, -331.9976, 0, 0, 0, 2, Maurian_Sureshot
P -1661.0744, -1912.0705, -331.9976, 0, 0, 0, 2, R`vule_Aukorrse
P -1651.4135, -1993.0031, -331.9976, 0, 0, 0, 2, Luneviere_Elianos
P -1488.5921, -1750.4826, -331.9976, 0, 0, 0, 2, Artan_Bloodforge
P -1260.3985, -1920.3648, -331.9976, 0, 0, 0, 2, War_Forge_Assistant_(Roam)
P -1149.7398, -2001.6159, -331.9976, 0, 0, 0, 2, Gnaap_Guinders
P -1236.2746, -1454.6778, -331.9976, 0, 0, 0, 2, The_Diaku_Armorer
P -999.6459, -1341.5103, -330.4757, 0, 0, 0, 2, Soke_of_Storms
P -1384.1900, -35.0001, -304.5308, 240, 127, 0, 2, The_Diaku_Overseer_(Raid)
P -1590.8299, 851.5311, -288.8613, 240, 127, 0, 2, Glykus_Helmir_(Raid)
P -486.7841, -20.8594, 167.5365, 240, 127, 0, 2, Rallos_Zek_(Raid)
P -705.9217, -9.9538, -294.0165, 240, 127, 0, 2, Rallos_Zek_the_Warlord_(Raid,After_Death_of_Rallos_Zek_on_top_floor)
P -1383.7391, -2175.8413, -330.4409, 240, 127, 0, 2, Tagrin_Maldric_(Raid)
P -389.4403, 100.4774, 167.5395, 240, 127, 0, 2, Tallon_Zek_(Raid,Spawn)
P 89.4487, -371.6921, 64.0018, 127, 64, 0, 2, Anival_the_Blade_(Hunter)
P -990.7032, -561.8664, 133.1346, 127, 64, 0, 2, Hinvat_Deathbringer_(Hunter)
P 93.7508, 643.8293, 74.0018, 127, 64, 0, 2, Shadow_Master_Vinta_(Hunter)
P 824.0921, -1983.9315, 202.0018, 240, 127, 0, 2, Vallon_Zek_(Raid)
P -1608.0000, -340.0000, 65.0000, 127, 64, 0, 2, The_Diaku_Supplier_(Hunter)
P -899.0000, -271.0000, -326.0000, 127, 64, 0, 2, a_frenized_initiate_(Hunter,Roam)
P -752.0000, -80.0000, -319.0000, 127, 64, 0, 2, a_Stampeding_Piglet_(Hunter,Roam)
P -624.0000, 58.0000, -315.0000, 127, 64, 0, 2, Zelrin_Morlock_(Hunter,Roam)
P -896.0000, 185.0000, -288.0000, 127, 64, 0, 2, an_Enraged_War_Boar_(Hunter,Roam)
P 89.6266, 349.8398, 64.0018, 127, 64, 0, 2, Shadow_Master_Vinta_(Hunter)
P 618.7241, 1959.5040, 192.0018, 0, 0, 0, 2, Tallon_Zek
P 275.3308, -661.9329, 74.0018, 127, 64, 0, 2, Anival_the_Blade_(Hunter)
