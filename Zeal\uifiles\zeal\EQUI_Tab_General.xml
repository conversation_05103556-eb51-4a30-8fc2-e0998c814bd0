<?xml version="1.0" encoding="us-ascii"?>
<XML ID="EQInterfaceDefinitionLanguage">
  <Schema xmlns="EverQuestData" xmlns:dt="EverQuestDataTypes" />
<Button item="Zeal_HideCorpse">
    <ScreenID>Zeal_HideCorpse</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>10</X>
      <Y>24</Y>
    </Location>
    <Size>
      <CX>150</CX>
      <CY>20</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Hides a corpse after you have looted it</TooltipReference>
    <Text>Hide corpse looted</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
   <Button item="Zeal_BlueCon">
    <ScreenID>Zeal_BlueCon</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>10</X>
      <Y>46</Y>
    </Location>
    <Size>
      <CX>150</CX>
      <CY>20</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Changes the blue con color to usercolor #70 which is otherwise unused, you can edit in the options window.</TooltipReference>
    <Text>Blue Con</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <Button item="Zeal_Timestamp">
    <ScreenID>Zeal_Timestamp</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>10</X>
      <Y>76</Y>
    </Location>
    <Size>
      <CX>150</CX>
      <CY>20</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Shows message timestamps</TooltipReference>
    <Text>Chat timestamps</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <Button item="Zeal_Input">
    <ScreenID>Zeal_Input</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>10</X>
      <Y>68</Y>
    </Location>
    <Size>
      <CX>150</CX>
      <CY>20</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Toggles the zeal input setup for any input in game, giving you a more modern input (ctrl+c, ctrl+v, left, right, shift left+right for highlighting, home, end ect)</TooltipReference>
    <Text>Advanced Input</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <Button item="Zeal_ShowHelm">
    <ScreenID>Zeal_ShowHelm</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>10</X>
      <Y>90</Y>
    </Location>
    <Size>
      <CX>150</CX>
      <CY>20</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Toggles your helmet</TooltipReference>
    <Text>Show Helm</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <Button item="Zeal_Escape">
    <ScreenID>Zeal_Escape</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>10</X>
      <Y>112</Y>
    </Location>
    <Size>
      <CX>150</CX>
      <CY>20</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Stops windows from closing on escape but still drops target</TooltipReference>
    <Text>Escape Logic</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <Button item="Zeal_AltContainerTooltips">
    <ScreenID>Zeal_AltContainerTooltips</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>10</X>
      <Y>156</Y>
    </Location>
    <Size>
      <CX>150</CX>
      <CY>20</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Alt key will show ALL item Tooltips in open bags</TooltipReference>
    <Text>Container Tooltips</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <Button item="Zeal_SpellbookAutoStand">
    <ScreenID>Zeal_SpellbookAutoStand</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>10</X>
      <Y>178</Y>
    </Location>
    <Size>
      <CX>150</CX>
      <CY>20</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Toggles Autostand if Spellbook is open (so you can turn pages with movement keys)</TooltipReference>
    <Text>Spellbook Auto Stand</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
    <Button item="Zeal_RightClickToEquip">
    <ScreenID>Zeal_RightClickToEquip</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>10</X>
      <Y>200</Y>
    </Location>
    <Size>
      <CX>150</CX>
      <CY>20</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
        <TooltipReference>Equip items from your bags by right clicking them.</TooltipReference>
    <Text>Right Click to Equip</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <!-- Hide Corpse options -->
  <Label item="Zeal_Timestamps_Label">
    <ScreenID>Zeal_Timestamps_Label</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>194</X>
      <Y>75</Y>
    </Location>
    <Size>
      <CX>150</CX>
      <CY>14</CY>
    </Size>
    <Text>Timestamps</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>false</AlignRight>
  </Label>
  <Combobox item="Zeal_Timestamps_Combobox">
    <ScreenID>Zeal_Timestamps_Combobox</ScreenID>
    <DrawTemplate>WDT_Inner</DrawTemplate>
    <Location>
      <X>194</X>
      <Y>95</Y>
    </Location>
    <Size>
      <CX>150</CX>
      <CY>24</CY>
    </Size>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ListHeight>70</ListHeight>
    <Button>BDT_Combo</Button>
    <Style_Border>true</Style_Border>
    <Choices>None</Choices>
    <Choices>Long</Choices>
    <Choices>Short</Choices>
  </Combobox>
  <Label item="Zeal_InviteSound_Label">
    <ScreenID>Zeal_InviteSound_Label</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>194</X>
      <Y>125</Y>
    </Location>
    <Size>
      <CX>145</CX>
      <CY>14</CY>
    </Size>
    <Text>Invite sound</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>false</AlignRight>
  </Label>
  <Combobox item="Zeal_InviteSound_Combobox">
    <ScreenID>Zeal_InviteSound_Combobox</ScreenID>
    <DrawTemplate>WDT_Inner</DrawTemplate>
    <Location>
      <X>194</X>
      <Y>145</Y>
    </Location>
    <Size>
      <CX>150</CX>
      <CY>24</CY>
    </Size>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ListHeight>70</ListHeight>
    <Button>BDT_Combo</Button>
    <Style_Border>true</Style_Border>
    <Choices>None</Choices>
  </Combobox>
  <Label item="Zeal_TellSound_Label">
    <ScreenID>Zeal_TellSound_Label</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>194</X>
      <Y>175</Y>
    </Location>
    <Size>
      <CX>145</CX>
      <CY>14</CY>
    </Size>
    <Text>New tell sound</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>false</AlignRight>
  </Label>
  <Combobox item="Zeal_TellSound_Combobox">
    <ScreenID>Zeal_TellSound_Combobox</ScreenID>
    <DrawTemplate>WDT_Inner</DrawTemplate>
    <Location>
      <X>194</X>
      <Y>195</Y>
    </Location>
    <Size>
      <CX>150</CX>
      <CY>24</CY>
    </Size>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ListHeight>70</ListHeight>
    <Button>BDT_Combo</Button>
    <Style_Border>true</Style_Border>
    <Choices>None</Choices>
  </Combobox>
  <Label item="Zeal_VersionLabel">
    <ScreenID>Zeal_VersionLabel</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>136</X>
      <Y>1</Y>
    </Location>
    <Size>
      <CX>77</CX>
      <CY>16</CY>
    </Size>
    <Text>Zeal Version:</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>false</AlignRight>
  </Label>
  <Label item="Zeal_VersionValue">
    <ScreenID>Zeal_VersionValue</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>212</X>
      <Y>1</Y>
    </Location>
    <Size>
      <CX>165</CX>
      <CY>16</CY>
    </Size>
    <Text>Not Installed</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>0</B>
      <Alpha>255</Alpha>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>false</AlignRight>
  </Label>
  <Button item="Zeal_RaidEscapeLock">
    <ScreenID>Zeal_RaidEscapeLock</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>10</X>
      <Y>134</Y>
    </Location>
    <Size>
      <CX>150</CX>
      <CY>20</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Pressing Escape will not close the raid window when this option is enabled.</TooltipReference>
    <Text>Raid Escape Lock</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <!-- Zeal Classic Classes -->
  <Button item="Zeal_ClassicClasses">
    <ScreenID>Zeal_ClassicClasses</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>10</X>
      <Y>222</Y>
    </Location>
    <Size>
      <CX>150</CX>
      <CY>20</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Toggles on and off the 50+ class names on /who and other areas</TooltipReference>
    <Text>Classic Class Names</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <!-- Zeal Tell Windows -->
  <Button item="Zeal_TellWindows">
    <ScreenID>Zeal_TellWindows</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>10</X>
      <Y>244</Y>
    </Location>
    <Size>
      <CX>150</CX>
      <CY>20</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Toggles Tell Windows</TooltipReference>
    <Text>Tell Windows</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
    <Button item="Zeal_TellWindowsHist">
    <ScreenID>Zeal_TellWindowsHist</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>10</X>
      <Y>266</Y>
    </Location>
    <Size>
      <CX>150</CX>
      <CY>20</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Toggles Tell Window Session History</TooltipReference>
    <Text>Tell Window History</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
   
  <Button item="Zeal_LinkAllAltDelimiter">
    <ScreenID>Zeal_LinkAllAltDelimiter</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>10</X>
      <Y>288</Y>
    </Location>
    <Size>
      <CX>150</CX>
      <CY>20</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Enables alternative link all delimiter</TooltipReference>
    <Text>Alt LinkAll Delimiter</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <Button item="Zeal_EnableContainerLock">
    <ScreenID>Zeal_EnableContainerLock</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>10</X>
      <Y>310</Y>
    </Location>
    <Size>
      <CX>150</CX>
      <CY>20</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Enables lock in container context menu (requires the bag to be re-opened to take effect)</TooltipReference>
    <Text>Enable container lock</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <Button item="Zeal_ExportOnCamp">
    <ScreenID>Zeal_ExportOnCamp</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>10</X>
      <Y>332</Y>
    </Location>
    <Size>
      <CX>150</CX>
      <CY>20</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Exports current inventory and spellbook to files when /camp is executed</TooltipReference>
    <Text>Export data on /camp</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <Button item="Zeal_SelfClickThru">
    <ScreenID>Zeal_SelfClickThru</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>10</X>
      <Y>354</Y>
    </Location>
    <Size>
      <CX>150</CX>
      <CY>20</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Disables third person click on self (/selfclickthru)</TooltipReference>
    <Text>Self-click thru</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <Button item="Zeal_BuffTimers">
    <ScreenID>Zeal_BuffTimers</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>10</X>
      <Y>376</Y>
    </Location>
    <Size>
      <CX>150</CX>
      <CY>20</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Toggles Buff Timers</TooltipReference>
    <Text>Buff Timers</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <Button item="Zeal_RecastTimers">
    <ScreenID>Zeal_RecastTimers</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>10</X>
      <Y>398</Y>
    </Location>
    <Size>
      <CX>150</CX>
      <CY>20</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Toggles Recast Timers</TooltipReference>
    <Text>Recast Timers</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <Button item="Zeal_RecastTimersLeftAlign">
    <ScreenID>Zeal_RecastTimersLeftAlign</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>10</X>
      <Y>420</Y>
    </Location>
    <Size>
      <CX>150</CX>
      <CY>20</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Left align recast timer tooltip</TooltipReference>
    <Text>Left Align Recast</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <Button item="Zeal_CtrlRightClickCorpse">
    <ScreenID>Zeal_CtrlRightClickCorpse</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>10</X>
      <Y>442</Y>
    </Location>
    <Size>
      <CX>150</CX>
      <CY>20</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Require Ctrl with right click to loot corpse</TooltipReference>
    <Text>Ctrl Right Click Loot</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <Button item="Zeal_CtrlContextMenus">
    <ScreenID>Zeal_CtrlContextMenus</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>10</X>
      <Y>464</Y>
    </Location>
    <Size>
      <CX>150</CX>
      <CY>20</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Require Ctrl with right click to pop-up context menus</TooltipReference>
    <Text>Ctrl Context Menus</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <Button item="Zeal_InviteDialog">
    <ScreenID>Zeal_InviteDialog</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>10</X>
      <Y>486</Y>
    </Location>
    <Size>
      <CX>150</CX>
      <CY>20</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Display a dialog when invited to a group</TooltipReference>
    <Text>Invite Dialog</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <Button item="Zeal_AutoFollowEnable">
    <ScreenID>Zeal_AutoFollowEnable</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>10</X>
      <Y>508</Y>
    </Location>
    <Size>
      <CX>150</CX>
      <CY>20</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Enable Zeal /follow mode (reliability)</TooltipReference>
    <Text>Patch /follow</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>  
  <Button item="Zeal_CastAutoStand">
    <ScreenID>Zeal_CastAutoStand</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>194</X>
      <Y>222</Y>
    </Location>
    <Size>
      <CX>150</CX>
      <CY>20</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Autostand when trying to cast</TooltipReference>
    <Text>Cast Auto Stand</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <Button item="Zeal_BrownSkeletons">
    <ScreenID>Zeal_BrownSkeletons</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>194</X>
      <Y>244</Y>
    </Location>
    <Size>
      <CX>150</CX>
      <CY>20</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Toggles brown skeleton models</TooltipReference>
    <Text>Brown Skeletons</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <Button item="Zeal_ClassicMusic">
    <ScreenID>Zeal_ClassicMusic</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>194</X>
      <Y>266</Y>
    </Location>
    <Size>
      <CX>150</CX>
      <CY>20</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Toggle Classic Music</TooltipReference>
    <Text>Classic Music</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <Button item="Zeal_SuppressMissedNotes">
    <ScreenID>Zeal_SuppressMissedNotes</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>194</X>
      <Y>288</Y>
    </Location>
    <Size>
      <CX>150</CX>
      <CY>20</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Suppress missed note messages from other bards</TooltipReference>
    <Text>No missed notes</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <Button item="Zeal_SuppressOtherFizzles">
    <ScreenID>Zeal_SuppressOtherFizzles</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>194</X>
      <Y>310</Y>
    </Location>
    <Size>
      <CX>150</CX>
      <CY>20</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Suppress fizzle messages from non-grouped casters</TooltipReference>
    <Text>Reduce fizzles</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>  
  <Button item="Zeal_UseZealAssistOn">
    <ScreenID>Zeal_UseZealAssistOn</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>194</X>
      <Y>332</Y>
    </Location>
    <Size>
      <CX>150</CX>
      <CY>20</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Enable per character /assist on settings</TooltipReference>
    <Text>Use Zeal /assist on</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <Button item="Zeal_DetectAssistFailure">
    <ScreenID>Zeal_DetectAssistFailure</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>194</X>
      <Y>354</Y>
    </Location>
    <Size>
      <CX>150</CX>
      <CY>20</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Emits a message and clears target if /assist fails</TooltipReference>
    <Text>Detect assist failure</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <Button item="Zeal_SingleClickGiveEnable">
    <ScreenID>Zeal_SingleClickGiveEnable</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>194</X>
      <Y>376</Y>
    </Location>
    <Size>
      <CX>150</CX>
      <CY>20</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Enables the single click auto-transfer of stackable items to open give, trade, or crafting windows</TooltipReference>
    <Text>Single click give</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <Button item="Zeal_EnhancedSpellInfo">
    <ScreenID>Zeal_EnhancedSpellInfo</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>194</X>
      <Y>398</Y>
    </Location>
    <Size>
      <CX>150</CX>
      <CY>20</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Replaces the spell info display content with 'enhanced' information</TooltipReference>
    <Text>Enhanced spell info</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <Label item="Zeal_FPS_Label">
    <ScreenID>Zeal_FPS_Label</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>194</X>
      <Y>420</Y>
    </Location>
    <Size>
      <CX>150</CX>
      <CY>14</CY>
    </Size>
    <Text>FPS Limit</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>false</AlignRight>
  </Label>
  <Combobox item = "Zeal_FPS_Combobox">
		<ScreenID>Zeal_FPS_Combobox</ScreenID>
		<Location>
			<X>194</X>
			<Y>440</Y>
		</Location>
		<Size>
			<CX>140</CX>
			<CY>25</CY>
		</Size>
		<TextColor>
			<R>255</R>
			<G>255</G>
			<B>255</B>
		</TextColor>
		<ListHeight>60</ListHeight>
		<Button>BDT_Combo</Button>
		<Style_Border>true</Style_Border>
		<Choices>Unlimited</Choices>
		<Choices>30</Choices>
		<Choices>60</Choices>
		<Choices>120</Choices>
		<Choices>144</Choices>
		<Choices>165</Choices>
		<Choices>240</Choices>
	</Combobox>
  <!-- end -->
    
  <Page item="Tab_General">
    <ScreenID>Tab_General</ScreenID>
    <RelativePosition>true</RelativePosition>
    <!-- Pages are sized and positioned by their parent tab -->
    <Style_VScroll>true</Style_VScroll>
    <Style_HScroll>true</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <DrawTemplate>WDT_Def</DrawTemplate>
    <TabText>General</TabText>
    <TabTextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TabTextColor>
    <TabTextActiveColor>
      <R>255</R>
      <G>255</G>
      <B>0</B>
    </TabTextActiveColor>
    <Style_Sizable>true</Style_Sizable>
    <Pieces>Zeal_HideCorpse</Pieces>
    <Pieces>Zeal_BlueCon</Pieces>
    <!--<Pieces>Zeal_Timestamp</Pieces>-->
    <Pieces>Zeal_Input</Pieces>
    <Pieces>Zeal_ShowHelm</Pieces>
    <Pieces>Zeal_Escape</Pieces>
    <Pieces>Zeal_HoverTimeout_Label</Pieces>
    <Pieces>Zeal_HoverTimeout_Slider</Pieces>
    <Pieces>Zeal_HoverTimeout_Value</Pieces>
    <Pieces>Zeal_AltContainerTooltips</Pieces>
    <Pieces>Zeal_SpellbookAutoStand</Pieces>
    <Pieces>Zeal_RightClickToEquip</Pieces>
    <Pieces>Zeal_Timestamps_Label</Pieces>
    <Pieces>Zeal_VersionLabel</Pieces>
    <Pieces>Zeal_VersionValue</Pieces>
    <Pieces>Zeal_RaidEscapeLock</Pieces>
    <Pieces>Zeal_ClassicClasses</Pieces>
    <Pieces>Zeal_TellWindows</Pieces>
    <Pieces>Zeal_TellWindowsHist</Pieces>
    <Pieces>Zeal_LinkAllAltDelimiter</Pieces>
    <Pieces>Zeal_EnableContainerLock</Pieces>
    <Pieces>Zeal_ExportOnCamp</Pieces>
    <Pieces>Zeal_SelfClickThru</Pieces>
    <Pieces>Zeal_BuffTimers</Pieces>
    <Pieces>Zeal_RecastTimers</Pieces>
    <Pieces>Zeal_RecastTimersLeftAlign</Pieces>
    <Pieces>Zeal_CtrlRightClickCorpse</Pieces>
    <Pieces>Zeal_CtrlContextMenus</Pieces>
    <Pieces>Zeal_InviteDialog</Pieces>
    <Pieces>Zeal_AutoFollowEnable</Pieces>
    <Pieces>Zeal_CastAutoStand</Pieces>
    <Pieces>Zeal_BrownSkeletons</Pieces>
    <Pieces>Zeal_ClassicMusic</Pieces>
    <Pieces>Zeal_SuppressMissedNotes</Pieces>
    <Pieces>Zeal_SuppressOtherFizzles</Pieces>
    <Pieces>Zeal_UseZealAssistOn</Pieces>
    <Pieces>Zeal_DetectAssistFailure</Pieces>
    <Pieces>Zeal_SingleClickGiveEnable</Pieces>
    <Pieces>Zeal_EnhancedSpellInfo</Pieces>
    <Pieces>Zeal_TellSound_Label</Pieces>
    <Pieces>Zeal_TellSound_Combobox</Pieces>
    <Pieces>Zeal_InviteSound_Label</Pieces>
    <Pieces>Zeal_InviteSound_Combobox</Pieces>
    <Pieces>Zeal_Timestamps_Combobox</Pieces>
	<Pieces>Zeal_FPS_Combobox</Pieces>
	<Pieces>Zeal_FPS_Label</Pieces>
    <Location>
      <X>0</X>
      <Y>22</Y>
    </Location>
    <Size>
      <CX>380</CX>
      <CY>339</CY>
    </Size>
  </Page>
  </XML>
