#pragma once
#include "hook_wrapper.h"
#include "memory.h"
#include "ZealSettings.h"
#include <unordered_map>
#include <string>
#include <unordered_set>

// Class to monitor charm pets and log when they break
class CharmMonitor
{
private:
    // Map to track charm pets by their spawn ID (spawn_id -> {pet_name, owner_name})
    std::unordered_map<short, std::pair<std::string, std::string>> charm_pets;

    // Track the most recently broken charm pet
    short last_broken_charm_id = 0;

    // Settings
    ZealSetting<bool> enabled = { true, "CharmMonitor", "Enabled", false };
    ZealSetting<std::string> ignored_owners = { "", "CharmMonitor", "IgnoredOwners", true };

    // Set of owner names to ignore notifications for
    std::unordered_set<std::string> ignored_owners_set;

    // Helper methods
    bool is_charm_pet(Zeal::EqStructures::Entity* entity);
    std::string get_pet_owner_name(Zeal::EqStructures::Entity* entity);
    void log_charm_break(const std::string& pet_name, const std::string& owner_name);
    void update_charm_pets();
    void list_group_pets();
    void toggle_owner_notification(const std::string& owner_name);
    void load_ignored_owners();
    void save_ignored_owners();
    bool is_owner_ignored(const std::string& owner_name);

public:
    CharmMonitor(ZealService* zeal);
    void on_zone();
    void main_loop();
    void target_last_broken_charm();
};
