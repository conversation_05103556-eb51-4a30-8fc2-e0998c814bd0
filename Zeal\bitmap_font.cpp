//--------------------------------------------------------------------------------------
// This file was inspired by DirectX 11 SpriteFont.cpp (MIT License)
// http://go.microsoft.com/fwlink/?LinkId=248929
//
// This is a simplified and rewritten implementation targeting DirectX8.
//--------------------------------------------------------------------------------------

// Generation of new font files:
// - Designed to use 'spritefont' files generated by MakeSpritetFont.exe.
//   - https://github.com/microsoft/DirectXTK/wiki/MakeSpriteFont
//   - Click on Downloads@Latest
//   - Run ./MakeSpriteFont "Arial" /FontSize:10 /TextureFormat:CompressedMono arial_10.spritefont

#define NOMINMAX
#include <fstream>
#include "EqFunctions.h"
#include "bitmap_font.h"
#include "default_spritefont.h"
#include "string_util.h"

namespace {

// Simple hack to identify queued glyphs for drop shadows using a very close to black color.
static constexpr D3DCOLOR kDropShadowColor = D3DCOLOR_XRGB(0x01, 0x01, 0x01);

static constexpr int kVertexBufferMaxBatchCount = 1000;
static constexpr int kVertexBufferMinBatchCount = 100;
static constexpr int kNumGlyphVertices = 4;  // Four per glyph using D3DPT_TRIANGLELIST with indices.
static constexpr int kNumGlyphIndices = 6;  // Three per triangle in D3DPT_TRIANGLELIST.
static constexpr int kNumGlyphTriangles = 2;  // Two triangles per glpyh to specify the rectangle.

// Hard-coded starting sequence of a MakeSpriteFont file.
static constexpr char kSpriteFontMagic[] = "DXTKfont";

// Helper class for parsing the binary SprintFont file stream.
class BinaryReader {
public:
    BinaryReader(const uint8_t* buffer, int size_bytes) : buffer(buffer), size_bytes(size_bytes) {}

    bool is_error() const { return read_error; }
    int bytes_left() const { return size_bytes - read_offset; }

    const uint8_t* read_bytes(size_t num_bytes) {
        if (read_offset + num_bytes > size_bytes) {
            read_error = true;
            return nullptr;
        }

        const uint8_t* result = &buffer[read_offset];
        read_offset += num_bytes;
        return result;
    }

    template <class T> inline T read() {
        T result = { 0 };
        uint32_t size = sizeof(T);

        if (read_offset + size > size_bytes) {
            read_error = true;
            return result;
        }

        uint8_t* dst = reinterpret_cast<uint8_t*>(&result);
        for (uint32_t i = 0; i < size; ++i)
            dst[i] = buffer[read_offset + i];
        read_offset += size;
        return result;
    }

public:
    const uint8_t* buffer;
    const int size_bytes;
    int read_offset = 0;
    bool read_error = false;
};

// Helper function for reading in a binary file into a std::vector buffer (for memory management).
std::vector<uint8_t> load_file(const char* filename) {
    uint32_t size_bytes = 0;
    std::ifstream in(filename, std::ifstream::binary);
    std::vector<uint8_t> buffer;
    if (in) {
        in.seekg(0, in.end);
        size_bytes = in.tellg();
        in.seekg(0, in.beg);
        buffer.resize(size_bytes);
        in.read(reinterpret_cast<char*>(buffer.data()), buffer.size());
        in.close();
    }
    return buffer;
}

} // namespace


// Factory for creating bitmap fonts. Returns nullptr if unsuccessful.
std::unique_ptr<BitmapFont> BitmapFont::create_bitmap_font(IDirect3DDevice8& device,
    const std::string& font_filename) {

    std::unique_ptr<BitmapFont> bitmap_font;

    // Attempt to load a filesystem font if there's a candidate name.
    if (!font_filename.empty() && font_filename != kDefaultFontName) {
        std::string full_filename =
            std::string(kFontDirectoryPath) + "/" + font_filename + kFontFileExtension;
        bitmap_font = std::make_unique<BitmapFont>(device, full_filename.c_str());
        if (!bitmap_font->is_valid()) {
            Zeal::EqGame::print_chat("Failed to load font file: %s", full_filename.c_str());
            bitmap_font.reset();  // Release the invalid font and nulls the ptr.
        }
        return bitmap_font;
    }

    // Initialize with the embedded default font.
    bitmap_font = std::make_unique<BitmapFont>(device,
        std::span<const uint8_t>(default_spritefont, default_spritefont_len));
    if (!bitmap_font->is_valid()) {
        bitmap_font.reset();  // Release the invalid font and nulls the ptr.
        Zeal::EqGame::print_chat("Error initializing default font");
    }
    return bitmap_font;
}


std::vector<std::string> BitmapFontBase::get_available_fonts() {
    const std::string directoryPath = kFontDirectoryPath;

    std::vector<std::string> fonts = { kDefaultFontName };  // "default" is always first in list.
    for (const auto& entry : std::filesystem::directory_iterator(directoryPath)) {
        if (entry.is_regular_file() && entry.path().extension() == kFontFileExtension) {
            fonts.push_back(entry.path().stem().string());  // Add filename without extension.
        }
    }

    return fonts;
}

// Calculates the default shadow offset for this font size.
float BitmapFontBase::calculate_shadow_offset() const {
    float offset = std::roundf(get_line_spacing() * shadow_offset_factor);
    return std::max(1.f, offset);  // Rounded, integer offset >= 1.
}

// Reads a font from files created with the MakeSpriteFont utility.
BitmapFontBase::BitmapFontBase(IDirect3DDevice8& device_in, const char* filename) :
    BitmapFontBase(device_in, load_file(filename)) {
}

// Parse the binary MakeSprintFont blob, initialize the glyphs table, and create the D3D texture.
BitmapFontBase::BitmapFontBase(IDirect3DDevice8& device, std::span<const uint8_t> data_span): device(device) {

    BinaryReader reader(data_span.data(), data_span.size());

    // Validate the binary blob header matches
    for (char const* magic = kSpriteFontMagic; *magic; magic++)
    {
        if (reader.read<uint8_t>() != *magic) {
            Zeal::EqGame::print_chat("Invalid SprintFont file");
            return;  // Abort.
        }
    }

    // Read the glyph data.
    auto num_glyphs = reader.read<uint32_t>();
    for (auto i = 0; i < num_glyphs; ++i) {
        auto glyph_data = reader.read<Glyph>();
        if (glyph_data.character >= kNumGlyphs)
            continue;  // Just skip non-ascii.
        glyph_table[glyph_data.character] = glyph_data;
    }

    line_spacing = reader.read<float>();
    line_spacing = static_cast<float>(static_cast<int>(line_spacing + 0.5f));  // ceil().

    // Read in the default character and set all uninitialized table entries to it.
    uint32_t file_default_character = reader.read<uint32_t>();
    default_character = (file_default_character < kNumGlyphs) ? file_default_character : '\0';
    for (int i = 0; i < kNumGlyphs; ++i) {
        if (glyph_table[i].character == '\0')  // Uninitialized.
            glyph_table[i] = glyph_table[default_character];
    }

    // Read the texture data.
    auto texture_width = reader.read<uint32_t>();
    auto texture_height = reader.read<uint32_t>();
    auto texture_format = reader.read<int32_t>();  // Note: DXGI_FORMAT, not D3D_FORMAT
    auto texture_stride = reader.read<uint32_t>();
    auto texture_rows = reader.read<uint32_t>();

    const uint64_t texture_data_size = uint64_t(texture_stride) * uint64_t(texture_rows);
    if (reader.is_error() || reader.bytes_left() < texture_data_size) {
        Zeal::EqGame::print_chat("Invalid SprintFont file texture data size");
        return;
    }
    auto texture_data = reader.read_bytes(static_cast<size_t>(texture_data_size));
    if (!texture_data) {
        Zeal::EqGame::print_chat("Invalid SprintFont file texture data");
        return;
    }

    // The MakeSpriteFont supports three DXGI_FORMATs:
    const int DXGI_FORMAT_R8G8B8A8_UNORM = 28;
    const int DXGI_FORMAT_B4G4R4A4_UNORM = 115;
    const int DXGI_FORMAT_BC2_UNORM = 74;
    if (texture_format != DXGI_FORMAT_BC2_UNORM) {
        Zeal::EqGame::print_chat("Only Font DXGI_FORMAT_BC2_UNORM is currently supported");
        return;
    }
    D3DFORMAT d3dformat = D3DFMT_DXT2;  // Equivalent to BC2_UNORM.

    // Create the D3D texture.
    RECT texture_rect = create_texture(
        texture_width, texture_height,
        d3dformat,
        texture_stride, texture_rows,
        texture_data);

    if (!texture) {
        Zeal::EqGame::print_chat("Failed to create the font bitmap texture");
        return;
    }

    // We customize four special glyph indices to support the stats bars. The background glyph's
    // advance is set to measure "zero" width so the value bar starts at the same location while
    // the value glyphs' advance is set to the full width size for the centering calcs.
    // The create_texture has created a solid 4x4 block at the end for the stats bars to use.
    RECT sub_rect = { texture_rect.right - 3, texture_rect.bottom - 2, texture_rect.right - 1,
                    texture_rect.bottom - 1};
    glyph_table[kStatsBarBackground] = { .character = kStatsBarBackground,
        .sub_rect = sub_rect, .x_offset = 0, .y_offset = 1,
        .x_advance = static_cast<float>(sub_rect.left - sub_rect.right) };
    glyph_table[kHealthBarValue] = { .character = kHealthBarValue,
        .sub_rect = sub_rect, .x_offset = 0, .y_offset = 1, .x_advance = kStatsBarWidth };
    glyph_table[kManaBarValue] = { .character = kManaBarValue,
        .sub_rect = sub_rect, .x_offset = 0, .y_offset = 1, .x_advance = kStatsBarWidth };
    glyph_table[kStaminaBarValue] = { .character = kStaminaBarValue,
        .sub_rect = sub_rect, .x_offset = 0, .y_offset = 1, .x_advance = kStatsBarWidth };
}

// Ensure all resources are released in the destructor.
BitmapFontBase::~BitmapFontBase() {
    release();
}

void BitmapFontBase::dump() const {
    Zeal::EqGame::print_chat("drop_shadow: %d, offset_factor: %.3f, outlined: %d, align_bottom: %d",
        drop_shadow, shadow_offset_factor, outlined, align_bottom);
    Zeal::EqGame::print_chat("line_spacing: %f, default_character: %c",
        line_spacing, default_character);

    if (texture) {
        Zeal::EqGame::print_chat("Texture:");
        D3DSURFACE_DESC desc;
        if (FAILED(texture->GetLevelDesc(0, &desc)))
            Zeal::EqGame::print_chat("Invalid");
        else {
            Zeal::EqGame::print_chat("Format: %d, Type: %d, Usage: %d",
                desc.Format, desc.Type, desc.Usage);
            Zeal::EqGame::print_chat("Width: %d, Height: %d, MultiSampleType: %d",
                desc.Width, desc.Height, desc.MultiSampleType);
        }
    }
}

// DirectX resources need to be manually released.
void BitmapFontBase::release() {
    if (texture)
        texture->Release();
    texture = nullptr;
    if (vertex_buffer)
        vertex_buffer->Release();
    vertex_buffer = nullptr;
    if (index_buffer)
        index_buffer->Release();
    index_buffer = nullptr;
    glyph_queue.clear();
}


// DirectX resources need to be manually released.
void BitmapFont::release() {
    BitmapFontBase::release();
    vertices.reset();
}

// Creates the D3D texture (acquires and configures resources).
RECT BitmapFontBase::create_texture(uint32_t width, uint32_t height,
    D3DFORMAT format, uint32_t stride, uint32_t rows,
    const uint8_t* data) {

    //Zeal::EqGame::print_chat("Font Texture: w: %i, h: %i, format: 0x%08x, stride: %i, rows: %i",
    //    width, height, format, stride, rows);

    const uint64_t size_bytes = uint64_t(stride) * uint64_t(rows);
    if (size_bytes > 256*1024ull)  // Just a sanity check.
        return RECT(0, 0, 0, 0);

    // Texture copy below assumes 4x4 packed DXT2 format.
    if (format != D3DFMT_DXT2 || (rows * 4 != height)) {
        Zeal::EqGame::print_chat("Font: Unsupported texture: fmt: 0x%08x, rows: %i, height: %i", format, rows, height);
        return RECT(0, 0, 0, 0);
    }

    uint32_t mip_levels = 1;
    const DWORD usage = 0;  // Not a render target or dynamic.
    uint32_t width_pow2 = std::bit_ceil(width);  // Use powers of two for HW portability.
    uint32_t height_pow2 = std::bit_ceil(height);

    auto hresult = D3DXCreateTexture(&device, width_pow2, height_pow2, mip_levels, usage, format,
        D3DPOOL_MANAGED, &texture);
    if (FAILED(hresult)) {
        Zeal::EqGame::print_chat("Font texture failure: w: %i, h: %i, code: 0x%08x",
            width_pow2, height_pow2, hresult);
        texture = nullptr;  // Ensure it is nulled.
        return RECT(0, 0, 0, 0);
    }

    // Cache away conversion factors for use in vertex texture coordinates.
    Vec2 texture_size = Vec2(static_cast<float>(width_pow2), static_cast<float>(height_pow2));
    inverse_texture_size = Vec2(1.f / texture_size.x, 1.f / texture_size.y);

    D3DLOCKED_RECT locked_rect;
    if (FAILED(texture->LockRect(0, &locked_rect, NULL, D3DLOCK_DISCARD)) ||
        locked_rect.Pitch < stride) {
        Zeal::EqGame::print_chat("Font texture: Lock failed");
        texture->Release();
        texture = nullptr;
        return RECT(0, 0, 0, 0);
    }

    // Note: This uses rows, not height, and stride to copy over the data assuming this
    // is properly packed DXT2 data. We checked above that 4 * rows = height and stride <= pitch.
    uint8_t* texture_data = reinterpret_cast<uint8_t*>(locked_rect.pBits);
    for (int y = 0; y < rows; ++y)
        memcpy(&texture_data[y * locked_rect.Pitch], &data[y * stride], stride);
  

    // To support the health bar, we make a solid block at the very end of the texture. This
    // is going to be unused in all likely scenarios due to the power of two ceiling above.
    int last_row = height_pow2 / 4 - 1;  // Rows = height / 4 since 4x4 block compression.
    int last_block = 4 * width_pow2  - 16;
    if (last_block + 16 <= locked_rect.Pitch)
        memset(&texture_data[last_row * locked_rect.Pitch + last_block], 0xff, 16);
    else
        Zeal::EqGame::print_chat("Error: Unable to set health bar texture (%i vs %i)", locked_rect.Pitch, last_block);

    texture->UnlockRect(0);
    return RECT(0, 0, width_pow2, height_pow2);
}

// Returns the glyph details for the character (or default if out of range).
const BitmapFontBase::Glyph* BitmapFontBase::get_glyph(char character) const {
    if (character >= kNumGlyphs)
        character = default_character;
    return &glyph_table[character];
}

// The core glyph layout algorithm shared by the string functions.
template<typename TAction>
void BitmapFontBase::for_each_glyph(const char* text, TAction action) const
{
    float x = 0;
    float y = 0;

    int length_limit = 100;  // Limit text strings to 100 characters.
    for (; *text && length_limit--; text++) {
        const char character = *text;

        switch (character)
        {
        case '\r':
            continue;            // Skip carriage returns.

        case '\n':
            x = 0;
            y += line_spacing;              // New line.
            break;

        default:
            auto glyph = get_glyph(character);
            x += glyph->x_offset;
            if (x < 0)
                x = 0;
            if (((glyph->sub_rect.right - glyph->sub_rect.left) > 1)
                            || ((glyph->sub_rect.bottom - glyph->sub_rect.top) > 1))
                action(glyph, x, y + glyph->y_offset);
            const float advance = float(glyph->sub_rect.right) - float(glyph->sub_rect.left) + glyph->x_advance;
            x += advance;
            break;
        }
    }
}

void BitmapFontBase::queue_lines(const std::vector<Lines>& lines, D3DCOLOR color, Vec2 offset)
{
    for (const auto& line : lines) {
        Vec2 upper_left = line.upper_left + offset;
        for_each_glyph(line.text.c_str(),
            [&](const Glyph* glyph, float x, float y) {
                if ((glyph->character == kStatsBarBackground || glyph->character == kHealthBarValue
                    || glyph->character == kManaBarValue || glyph->character == kStaminaBarValue)
                    && color == kDropShadowColor)
                    return;  // Skip drop shadow for the health bar.
                glyph_queue.push_back({ glyph, upper_left + Vec2(x,y), color });
            });
    }
}

// Public interface that queues a string for later rendering in the flush call.
void BitmapFontBase::queue_string(const char* text, const Vec3& position, bool center,
                             const D3DCOLOR color, bool grid_align) {
    if (!text || !(*text))
        return;  // Skip nullptr or empty strings.

    std::vector<Lines> lines;
    Vec2 upper_left(position.x, position.y);
    if (center && strchr(text, '\n') != nullptr) {
        // Split into the multiple lines and measure the width of each line.
        auto text_lines = Zeal::String::split_text(std::string(text));
        float x_max = 0;
        float y_height = 0;
        float y_advance = 0;
        for (const auto& line : text_lines) {
            y_height += y_advance;
            Vec3 size = measure_string(line.c_str());
            x_max = std::max(x_max, size.x);
            lines.push_back({ line, Vec2(size.x, y_height) });
            y_height += size.y;
            y_advance = std::max(0.f, size.z - size.y);  // Save if needed for next line.
        }
        float x_offset = -x_max * 0.5f;  // Common base offset for all lines.
        float y_offset = align_bottom ? -y_height : -y_height * 0.5f;
        for (auto& line : lines) {
            float x_line_offset = x_offset + 0.5f * (x_max - line.upper_left.x);
            float y_line_offset = line.upper_left.y + y_offset;
            line.upper_left = upper_left + Vec2(x_line_offset, y_line_offset);
        }
    }
    else {
        if (center) {
            Vec3 size = measure_string(text);
            upper_left -= Vec2(0.5f * size.x, align_bottom ? size.y : 0.5 * size.y);
        }
        lines.push_back({ std::string(text), upper_left });
    }
    if (grid_align) {
        for (auto& line : lines) {
            line.upper_left.x = std::round(line.upper_left.x);  // Starts need to be grid aligned for clean rendering.
            line.upper_left.y = std::round(line.upper_left.y);
        }
    }
    if (drop_shadow || outlined) {
        float shadow_offset = calculate_shadow_offset();
        queue_lines(lines, kDropShadowColor, Vec2(shadow_offset, shadow_offset));
        if (outlined) {
            // Technically would be cleaner with left, right, top, bottom adjusts (4 passes).
            queue_lines(lines, kDropShadowColor, Vec2(-shadow_offset, -shadow_offset));
        }
    }
    queue_lines(lines, color);
}

// Returns the height and width of the string. Does not include line spacing, which is returned as z.
Vec3 BitmapFontBase::measure_string(const char* text) const
{
    float line_height = line_spacing;  // Default line height.
    Vec3 result{ 0, 0, 0 };
    if (!text || !(*text))
        return result;  // Skip nullptr or empty strings.

    // for_each_glyph resets x for each line, y for each glyph, and includes the offsets in x and y.
    for_each_glyph(text,
        [&](Glyph const* glyph, float x, float y) {
            float w = static_cast<float>(glyph->sub_rect.right - glyph->sub_rect.left);
            float h = static_cast<float>(glyph->sub_rect.bottom - glyph->sub_rect.top);
            if (glyph->character == kHealthBarValue || glyph->character == kManaBarValue ||
                glyph->character == kStaminaBarValue) {
                w = glyph->x_advance;
                h = kStatsBarHeight;
                line_height = kStatsBarHeight + 1;
            }
            result = Vec3(std::max(result.x, x + w), std::max(result.y, y + h), line_height);
        });

    return result;
}

// Returns the bounding box around the string.
RECT BitmapFontBase::measure_draw_rect(const char* text, const Vec2& position) const {
    if (!text || !(*text))
        return { 0, 0, 0, 0 };  // Skip nullptr or empty strings.

    RECT result = { LONG_MAX, LONG_MAX, 0, 0 };

    for_each_glyph(text,
        [&](const Glyph* glyph, float x, float y) {
            auto const w = static_cast<float>(glyph->sub_rect.right - glyph->sub_rect.left);
            auto const h = static_cast<float>(glyph->sub_rect.bottom - glyph->sub_rect.top);

            const float min_x = position.x + x;
            const float min_y = position.y + y + glyph->y_offset;
            const float advance = float(glyph->sub_rect.right) - float(glyph->sub_rect.left) + glyph->x_advance;
            const float max_x = min_x + std::max(advance, w);
            const float max_y = min_y + h;

            result.left = static_cast<long>(std::min(static_cast<float>(result.left), min_x));
            result.top = static_cast<long>(std::min(static_cast<float>(result.top), min_y));
            result.right = static_cast<long>(std::max(static_cast<float>(result.right), max_x));
            result.bottom = static_cast<long>(std::max(static_cast<float>(result.bottom), max_y));
        });

    if (result.left == LONG_MAX) {
        result.left = 0;
        result.top = 0;
    }

    return result;
}

// Renders all queued bitmap glyphs to the screen.
void BitmapFontBase::flush_queue_to_screen() {
    if (!texture) 
        glyph_queue.clear();

    if (glyph_queue.empty())
        return;

    if (!vertex_buffer) {
        if (FAILED(device.CreateVertexBuffer(
            kVertexBufferMaxBatchCount * kNumGlyphVertices * get_vertex_size(),
            D3DUSAGE_WRITEONLY | D3DUSAGE_DYNAMIC, get_fvf_code(),
            D3DPOOL_DEFAULT, &vertex_buffer))) {
            vertex_buffer = nullptr;  // Ensure nullptr.
            release();  // Disable future attempts.
            return;
        }
    }

    if (!index_buffer && !create_index_buffer()) {
        release();  // Disable future attempts.
        return;
    }

    render_queue();
    glyph_queue.clear();
}

// Submits glyph sprites to the GPU in batches.
void BitmapFont::render_queue() {
    if (!vertices)
        vertices = std::make_unique<GlyphVertex[]>(kVertexBufferMaxBatchCount * kNumGlyphVertices);

    // Configure for 2D drawing with alpha blending enabled.
    D3DRenderStateStash render_state(device);
    render_state.store_and_modify({ D3DRS_CULLMODE, D3DCULL_NONE });
    render_state.store_and_modify({ D3DRS_ALPHABLENDENABLE, TRUE }); // Note: Optional, could disable.
    render_state.store_and_modify({ D3DRS_SRCBLEND, D3DBLEND_SRCALPHA });
    render_state.store_and_modify({ D3DRS_DESTBLEND, D3DBLEND_INVSRCALPHA });
    render_state.store_and_modify({ D3DRS_ZENABLE, FALSE });  // Rely on render order.
    render_state.store_and_modify({ D3DRS_ZWRITEENABLE, FALSE });
    render_state.store_and_modify({ D3DRS_LIGHTING, FALSE });  // Disable lighting

    // Set texture stage states to avoid any unexpected texturing
    D3DTextureStateStash texture_state(device);
    texture_state.store_and_modify({ D3DTSS_COLOROP, D3DTOP_MODULATE });  // Mix color with white font.
    texture_state.store_and_modify({ D3DTSS_COLORARG1, D3DTA_TEXTURE });
    texture_state.store_and_modify({ D3DTSS_COLORARG2, D3DTA_DIFFUSE });
    texture_state.store_and_modify({ D3DTSS_ALPHAOP, D3DTOP_MODULATE });  // Support color alpha.
    texture_state.store_and_modify({ D3DTSS_ALPHAARG1, D3DTA_TEXTURE });
    texture_state.store_and_modify({ D3DTSS_ALPHAARG2, D3DTA_DIFFUSE });

    // Note: Not preserving shader, texture, source, or indices to avoid reference counting.
    device.SetVertexShader(GlyphVertex::kGlyphVertexFvfCode);
    device.SetTexture(0, texture);
    device.SetStreamSource(0, vertex_buffer, sizeof(GlyphVertex));

    int read_index = 0;
    while (read_index < glyph_queue.size()) {
        const int glyphs_left_count = glyph_queue.size() - read_index;
        int empty_space_count = kVertexBufferMaxBatchCount - vertex_buffer_wr_index;

        if ((glyphs_left_count > empty_space_count) && (empty_space_count < kVertexBufferMinBatchCount)) {
            vertex_buffer_wr_index = 0;  // Out of room, so wrap back to start.
            empty_space_count = kVertexBufferMaxBatchCount;
        }
        const int batch_count = std::min(glyphs_left_count, empty_space_count);
        if (batch_count < 1)
            break;  // Shouldn't happen, but if it does, just abort processing glyphs.

        for (int i = 0; i < batch_count; i++)
            calculate_glyph_vertices(glyph_queue[read_index + i], &vertices[i * kNumGlyphVertices]);

        auto lock_type = (vertex_buffer_wr_index == 0) ? D3DLOCK_DISCARD : D3DLOCK_NOOVERWRITE;
        const int start_vertex_index = vertex_buffer_wr_index * kNumGlyphVertices;
        const int num_batch_vertices = batch_count * kNumGlyphVertices;
        const int start_offset_bytes = start_vertex_index * sizeof(GlyphVertex);
        const int copy_size = num_batch_vertices * sizeof(GlyphVertex);
        BYTE* buffer = nullptr;
        if (FAILED(vertex_buffer->Lock(start_offset_bytes, copy_size, &buffer, lock_type))) {
            release();
            return;
        }
        memcpy(buffer, vertices.get(), copy_size);
        vertex_buffer->Unlock();

        device.SetIndices(index_buffer, 0);
        device.DrawIndexedPrimitive(D3DPT_TRIANGLELIST, start_vertex_index,
            num_batch_vertices, vertex_buffer_wr_index * kNumGlyphIndices,
            batch_count * kNumGlyphTriangles);
        read_index += batch_count;
        vertex_buffer_wr_index += batch_count;
    }

    // Restore D3D state.
    device.SetStreamSource(0, NULL, 0);  // Unbind vertex buffer.
    device.SetIndices(NULL, 0);  // Ensure index_buffer is no longer bound.
    device.SetTexture(0, NULL);  // Ensure texture is no longer bound.
    texture_state.restore_state();
    render_state.restore_state();
}

// Create the index buffer, lock it, fill it with fixed indices, and unlock.
bool BitmapFontBase::create_index_buffer() {
    if (index_buffer)
        return true;

    static_assert(kVertexBufferMaxBatchCount * kNumGlyphVertices < 0x7fff, "Exceeds 16-bit index");
    static_assert(kNumGlyphIndices == 6);  // Assumed below.
    static_assert(kNumGlyphVertices == 4);  // Assumed below.

    if (FAILED(device.CreateIndexBuffer(
        kVertexBufferMaxBatchCount * kNumGlyphIndices * sizeof(int16_t),
        0, D3DFMT_INDEX16, D3DPOOL_DEFAULT, &index_buffer))) {
        index_buffer = nullptr;  // Ensure nullptr.
        return false;
    }

    uint8_t* locked_buffer = nullptr;
    if (FAILED(index_buffer->Lock(0, 0, &locked_buffer, D3DLOCK_DISCARD))) {
        release();
        return false;
    }

    // Fill it with a fixed pattern that maps six indices to the four vertices of the
    // two triangles needed for each glyph.  Indexing saves memory and enables caching.
    int16_t* indices = reinterpret_cast<int16_t*>(locked_buffer);
    for (size_t i = 0; i < kVertexBufferMaxBatchCount; ++i) {
        indices[i * kNumGlyphIndices + 0] = i * kNumGlyphVertices + 0;
        indices[i * kNumGlyphIndices + 1] = i * kNumGlyphVertices + 1;
        indices[i * kNumGlyphIndices + 2] = i * kNumGlyphVertices + 2;
        indices[i * kNumGlyphIndices + 3] = i * kNumGlyphVertices + 1;
        indices[i * kNumGlyphIndices + 4] = i * kNumGlyphVertices + 3;
        indices[i * kNumGlyphIndices + 5] = i * kNumGlyphVertices + 2;
    }
    index_buffer->Unlock();
    return true;
}

// Calculates the vertices required to place the glyph on the screen with the correct texture map.
void BitmapFont::calculate_glyph_vertices(const GlyphQueueEntry& entry,
                                            GlyphVertex glyph_vertices[4]) const {
    // Note: Transformed vertices require a -0.5 offset to properly align texels with pixels.
    // Vertices in xy 00, 10, 01, 11 order.
    static_assert(kNumGlyphVertices == 4);
    glyph_vertices[0].x = entry.position.x - 0.5f;
    glyph_vertices[1].x = entry.position.x - 0.5f
                          + entry.glyph->sub_rect.right - entry.glyph->sub_rect.left;
    glyph_vertices[2].x = glyph_vertices[0].x;
    glyph_vertices[3].x = glyph_vertices[1].x;
    
    glyph_vertices[0].u = entry.glyph->sub_rect.left * inverse_texture_size.x;
    glyph_vertices[1].u = entry.glyph->sub_rect.right * inverse_texture_size.x;
    glyph_vertices[2].u = glyph_vertices[0].u;
    glyph_vertices[3].u = glyph_vertices[1].u;

    glyph_vertices[0].y = entry.position.y - 0.5f;
    glyph_vertices[1].y = glyph_vertices[0].y;
    glyph_vertices[2].y = entry.position.y - 0.5f
                          + entry.glyph->sub_rect.bottom - entry.glyph->sub_rect.top;
    glyph_vertices[3].y = glyph_vertices[2].y;

    glyph_vertices[0].v = entry.glyph->sub_rect.top * inverse_texture_size.y;
    glyph_vertices[1].v = glyph_vertices[0].v;
    glyph_vertices[2].v = entry.glyph->sub_rect.bottom * inverse_texture_size.y;
    glyph_vertices[3].v = glyph_vertices[2].v;

    for (int i = 0; i < kNumGlyphVertices; ++i) {
        glyph_vertices[i].z = 0.5f;
        glyph_vertices[i].rhw = 1.0f;
        glyph_vertices[i].color = entry.color;
    }
}


// Factory for creating spritefonts. Returns nullptr if unsuccessful.
std::unique_ptr<SpriteFont> SpriteFont::create_sprite_font(IDirect3DDevice8& device,
    const std::string& font_filename) {

    std::unique_ptr<SpriteFont> sprite_font;

    // Attempt to load a filesystem font if there's a candidate name.
    if (!font_filename.empty() && font_filename != kDefaultFontName) {
        std::string full_filename =
            std::string(kFontDirectoryPath) + "/" + font_filename + kFontFileExtension;
        sprite_font = std::make_unique<SpriteFont>(device, full_filename.c_str());
        if (!sprite_font->is_valid()) {
            Zeal::EqGame::print_chat("Failed to load font file: %s", full_filename.c_str());
            sprite_font.reset();  // Release the invalid font and nulls the ptr.
        }
        return sprite_font;
    }

    // Initialize with the embedded default font.
    sprite_font = std::make_unique<SpriteFont>(device,
        std::span<const uint8_t>(default_spritefont, default_spritefont_len));
    if (!sprite_font->is_valid()) {
        sprite_font.reset();  // Release the invalid font and nulls the ptr.
        Zeal::EqGame::print_chat("Error initializing default font");
    }
    return sprite_font;
}

// Public interface that queues a string for later rendering in the flush call.
void SpriteFont::queue_string(const char* text, const Vec3& position, bool center,
    const D3DCOLOR color, bool grid_align) {
    if (!text || !(*text))
        return;  // Skip nullptr or empty strings.

    int start_index = glyph_queue.size();
    BitmapFontBase::queue_string(text, Vec3(0, 0, 0), center, color, grid_align);
    int stop_index = glyph_queue.size();
    glyph_string_queue.push_back({ .position = position, .start_index = start_index,
        .stop_index = stop_index, .hp_percent = hp_percent, .mana_percent = mana_percent,
        .stamina_percent = stamina_percent});
}

// Renders all queued glyphs to the screen.
void SpriteFont::flush_queue_to_screen() {
    BitmapFontBase::flush_queue_to_screen();
    glyph_string_queue.clear();
}

// DirectX resources need to be manually released.
void SpriteFont::release() {
    BitmapFontBase::release();
    vertices.reset();
    glyph_string_queue.clear();
}

// Submits glyph sprites to the GPU in batches.
void SpriteFont::render_queue() {
    if (!vertices)
        vertices = std::make_unique<Glyph3DVertex[]>(kVertexBufferMaxBatchCount * kNumGlyphVertices);

    // Configure for 3D drawing with alpha blending enabled.
    D3DRenderStateStash render_state(device);
    render_state.store_and_modify({ D3DRS_CULLMODE, D3DCULL_NONE });
    render_state.store_and_modify({ D3DRS_ALPHABLENDENABLE, TRUE });
    render_state.store_and_modify({ D3DRS_SRCBLEND, D3DBLEND_SRCALPHA });
    render_state.store_and_modify({ D3DRS_DESTBLEND, D3DBLEND_INVSRCALPHA });
    render_state.store_and_modify({ D3DRS_BLENDOP, D3DBLENDOP_ADD });  // ??
    render_state.store_and_modify({ D3DRS_ZENABLE, TRUE });
    render_state.store_and_modify({ D3DRS_ZWRITEENABLE, TRUE });
    render_state.store_and_modify({ D3DRS_LIGHTING, FALSE });

    // Set texture stage states to avoid any unexpected texturing
    D3DTextureStateStash texture_state(device);
    texture_state.store_and_modify({ D3DTSS_COLOROP, D3DTOP_MODULATE });  // Mix color with white font.
    texture_state.store_and_modify({ D3DTSS_COLORARG1, D3DTA_TEXTURE });
    texture_state.store_and_modify({ D3DTSS_COLORARG2, D3DTA_DIFFUSE });
    texture_state.store_and_modify({ D3DTSS_ALPHAOP, D3DTOP_MODULATE });  // Support color alpha.
    texture_state.store_and_modify({ D3DTSS_ALPHAARG1, D3DTA_TEXTURE });
    texture_state.store_and_modify({ D3DTSS_ALPHAARG2, D3DTA_DIFFUSE });
    texture_state.store_and_modify({ D3DTSS_MINFILTER, D3DTEXF_LINEAR });

    // Note: Not preserving shader, texture, source, or indices to avoid reference counting.
    device.SetVertexShader(Glyph3DVertex::kFvfCode);
    device.SetTexture(0, texture);
    device.SetStreamSource(0, vertex_buffer, sizeof(Glyph3DVertex));

    D3DXMATRIX worldMatrix, viewMatrix, originalWorldMatrix;
    device.GetTransform(D3DTS_WORLD, &originalWorldMatrix);
    device.GetTransform(D3DTS_VIEW, &viewMatrix);

    // Need to scale the bitmap font to roughly the right size in world data.
    const float scale_factor = 0.025f; // Empirically set so arial_24_bold roughly matches client.
    D3DXMATRIX scaleMatrix, rotationMatrix, translationMatrix;
    D3DXMatrixScaling(&scaleMatrix, scale_factor, scale_factor, scale_factor);
    // And we need to rotate it so that it faces the camera.
    D3DXMatrixIdentity(&rotationMatrix);
    for (int row = 0; row < 3; row++)  // Transpose rotation components of camera view matrix.
        for (int col = 0; col < 3; col++)
            rotationMatrix(col, row) = viewMatrix(row, col);
    D3DXMATRIX mat_font_to_face_camera = scaleMatrix * rotationMatrix;
    for (const auto& entry : glyph_string_queue) {
        if (entry.stop_index > glyph_queue.size())
            break;

        // Per string translation from model space to world space.
        D3DXMatrixTranslation(&translationMatrix, entry.position.x, entry.position.y, entry.position.z);
        worldMatrix = mat_font_to_face_camera * translationMatrix;
        device.SetTransform(D3DTS_WORLD, &worldMatrix);
        hp_percent = entry.hp_percent;  // Recall the stats for access in a sub-method.
        mana_percent = entry.mana_percent;
        stamina_percent = entry.stamina_percent; 

        int read_index = entry.start_index;
        while (read_index < entry.stop_index) {
            const int glyphs_left_count = entry.stop_index - read_index;
            int empty_space_count = kVertexBufferMaxBatchCount - vertex_buffer_wr_index;

            if ((glyphs_left_count > empty_space_count) && (empty_space_count < kVertexBufferMinBatchCount)) {
                vertex_buffer_wr_index = 0;  // Out of room, so wrap back to start.
                empty_space_count = kVertexBufferMaxBatchCount;
            }
            const int batch_count = std::min(glyphs_left_count, empty_space_count);
            if (batch_count < 1)
                break;  // Shouldn't happen, but if it does, just abort processing glyphs.

            for (int i = 0; i < batch_count; i++)
                calculate_glyph_vertices(glyph_queue[read_index + i], &vertices[i * kNumGlyphVertices]);

            auto lock_type = (vertex_buffer_wr_index == 0) ? D3DLOCK_DISCARD : D3DLOCK_NOOVERWRITE;
            const int start_vertex_index = vertex_buffer_wr_index * kNumGlyphVertices;
            const int num_batch_vertices = batch_count * kNumGlyphVertices;
            const int start_offset_bytes = start_vertex_index * sizeof(Glyph3DVertex);
            const int copy_size = num_batch_vertices * sizeof(Glyph3DVertex);
            BYTE* buffer = nullptr;
            if (FAILED(vertex_buffer->Lock(start_offset_bytes, copy_size, &buffer, lock_type))) {
                release();
                return;
            }
            memcpy(buffer, vertices.get(), copy_size);
            vertex_buffer->Unlock();

            device.SetIndices(index_buffer, 0);
            device.DrawIndexedPrimitive(D3DPT_TRIANGLELIST, start_vertex_index,
                num_batch_vertices, vertex_buffer_wr_index * kNumGlyphIndices,
                batch_count * kNumGlyphTriangles);
            read_index += batch_count;
            vertex_buffer_wr_index += batch_count;
        }
    }

    // Restore D3D state.
    device.SetStreamSource(0, NULL, 0);  // Unbind vertex buffer.
    device.SetIndices(NULL, 0);  // Ensure index_buffer is no longer bound.
    device.SetTexture(0, NULL);  // Ensure texture is no longer bound.
    device.SetTransform(D3DTS_WORLD, &originalWorldMatrix);
    texture_state.restore_state();
    render_state.restore_state();
}


// Calculates the vertices required to place the glyph on the screen with the correct texture map.
void SpriteFont::calculate_glyph_vertices(const GlyphQueueEntry& entry,
    Glyph3DVertex glyph_vertices[4]) const {
    // Vertices in xy 00, 10, 01, 11 order.
    static_assert(kNumGlyphVertices == 4);
    float width = static_cast<float>(entry.glyph->sub_rect.right - entry.glyph->sub_rect.left);
    float height = static_cast<float>(entry.glyph->sub_rect.bottom - entry.glyph->sub_rect.top);
    float z = (entry.color == kDropShadowColor) ? +1.f : 0.0f;
    auto color = entry.color;
    if (entry.glyph->character == kStatsBarBackground) {
        z = -0.25f;  // Slightly in front of normal text.
        width = kStatsBarWidth;
        height = kStatsBarHeight;
        color = D3DCOLOR_ARGB(128, 128, 128, 128);
    }
    else if (entry.glyph->character == kHealthBarValue) {
        z = -0.5f;  // Slightly more in front.
        width = hp_percent * ((1.f / 100.f) * kStatsBarWidth);
        height = kStatsBarHeight;
        color = (hp_percent > 75) ? D3DCOLOR_XRGB(0, 192, 0) :  // Green
            (hp_percent > 50) ? D3DCOLOR_XRGB(192, 192, 0) :  // Yellow
            (hp_percent > 25) ? D3DCOLOR_XRGB(192, 96, 40) :  // Orange
            D3DCOLOR_XRGB(192, 0, 0);  // Red
    }
    else if (entry.glyph->character == kManaBarValue) {
        z = -0.5f;  // Slightly more in front.
        width = mana_percent * ((1.f / 100.f) * kStatsBarWidth);
        height = kStatsBarHeight;
        color = D3DCOLOR_XRGB(0, 0x40, 0xf0); // Use default CON_BLUE color.
    }
    else if (entry.glyph->character == kStaminaBarValue) {
        z = -0.5f;  // Slightly more in front.
        width = stamina_percent * ((1.f / 100.f) * kStatsBarWidth);
        height = kStatsBarHeight;
        color = D3DCOLOR_XRGB(240, 190, 11); // Yellow / honey.
    }

    glyph_vertices[0].x = entry.position.x;
    glyph_vertices[1].x = entry.position.x + width;
    glyph_vertices[2].x = glyph_vertices[0].x;
    glyph_vertices[3].x = glyph_vertices[1].x;

    glyph_vertices[0].u = entry.glyph->sub_rect.left * inverse_texture_size.x;
    glyph_vertices[1].u = entry.glyph->sub_rect.right * inverse_texture_size.x;
    glyph_vertices[2].u = glyph_vertices[0].u;
    glyph_vertices[3].u = glyph_vertices[1].u;

    glyph_vertices[0].y = entry.position.y;
    glyph_vertices[1].y = glyph_vertices[0].y;
    glyph_vertices[2].y = entry.position.y + height;
    glyph_vertices[3].y = glyph_vertices[2].y;

    glyph_vertices[0].v = entry.glyph->sub_rect.top * inverse_texture_size.y;
    glyph_vertices[1].v = glyph_vertices[0].v;
    glyph_vertices[2].v = entry.glyph->sub_rect.bottom * inverse_texture_size.y;
    glyph_vertices[3].v = glyph_vertices[2].v;

    for (int i = 0; i < kNumGlyphVertices; ++i) {
        glyph_vertices[i].z = z;
        glyph_vertices[i].color = color;
    }
}

