#include "charm_monitor.h"
#include "Zeal.h"
#include "EqStructures.h"
#include "EqAddresses.h"
#include "EqFunctions.h"
#include "string_util.h"
#include <algorithm>

CharmMonitor::CharmMonitor(ZealService* zeal)
{
    // Register callbacks
    zeal->callbacks->AddGeneric([this]() { main_loop(); }, callback_type::MainLoop);
    zeal->callbacks->AddGeneric([this]() { on_zone(); }, callback_type::Zone);
    zeal->callbacks->AddGeneric([this]() { load_ignored_owners(); }, callback_type::InitUI);

    // Add command to toggle charm break logging
    zeal->commands_hook->Add("/charmbreak", {"/cb"}, "Toggles charm break logging for group members",
        [this](std::vector<std::string>& args) {
            if (args.size() > 1) {
                if (Zeal::String::compare_insensitive(args[1], "on")) {
                    enabled.set(true);
                    Zeal::EqGame::print_chat("Charm break logging enabled");
                    return true;
                }
                else if (Zeal::String::compare_insensitive(args[1], "off")) {
                    enabled.set(false);
                    Zeal::EqGame::print_chat("Charm break logging disabled");
                    return true;
                }
            }

            // Toggle if no argument provided
            enabled.set(!enabled.get());
            Zeal::EqGame::print_chat("Charm break logging %s", enabled.get() ? "enabled" : "disabled");
            return true;
        });

    // Add debug command to list group pets
    zeal->commands_hook->Add("/listpets", {"/lp"}, "Lists all pets owned by group members",
        [this](std::vector<std::string>& args) {
            list_group_pets();
            return true;
        });

    // Add command to target the last broken charm pet
    zeal->commands_hook->Add("/targetbroken", {"/tb"}, "Targets the most recently broken charm pet",
        [this](std::vector<std::string>& args) {
            target_last_broken_charm();
            return true;
        });

    // Add command to toggle notifications for a specific pet owner
    zeal->commands_hook->Add("/ignorecharm", {"/ic"}, "Toggles charm break notifications for a specific pet owner",
        [this](std::vector<std::string>& args) {
            if (args.size() < 2) {
                // List all currently ignored owners
                if (ignored_owners_set.empty()) {
                    Zeal::EqGame::print_chat("No pet owners are currently ignored for charm break notifications");
                } else {
                    Zeal::EqGame::print_chat("Currently ignored pet owners:");
                    for (const auto& owner : ignored_owners_set) {
                        Zeal::EqGame::print_chat("  - %s", owner.c_str());
                    }
                }
                Zeal::EqGame::print_chat("Usage: /ignorecharm <owner_name> - Toggle notifications for a specific owner");
                return true;
            }

            // Get the owner name from the arguments
            std::string owner_name = args[1];
            toggle_owner_notification(owner_name);
            return true;
        });
}

void CharmMonitor::on_zone()
{
    // Clear the charm pets map when zoning
    charm_pets.clear();

    // Make sure the ignored owners list is loaded
    load_ignored_owners();
}

void CharmMonitor::main_loop()
{
    if (!enabled.get() || !Zeal::EqGame::is_in_game())
        return;

    // Update the list of charm pets and check for breaks
    update_charm_pets();
}

bool CharmMonitor::is_charm_pet(Zeal::EqStructures::Entity* entity)
{
    // Check if the entity is an NPC with a player owner
    if (!entity || entity->Type != Zeal::EqEnums::EntityTypes::NPC || !entity->PetOwnerSpawnId)
        return false;

    // Get the owner entity
    Zeal::EqStructures::Entity* owner = Zeal::EqGame::get_entity_by_id(entity->PetOwnerSpawnId);
    if (!owner || owner->Type != Zeal::EqEnums::EntityTypes::Player)
        return false;

    // Check if the owner is in the player's group
    const Zeal::EqStructures::GroupInfo* group_info = Zeal::EqGame::GroupInfo;
    if (!group_info->is_in_group())
        return false;

    // Check if the owner is the player or a group member
    if (owner == Zeal::EqGame::get_self())
        return false; // Skip player's own pet

    for (int i = 0; i < EQ_NUM_GROUP_MEMBERS; i++) {
        Zeal::EqStructures::Entity* group_member = group_info->EntityList[i];
        if (group_member && group_member == owner)
            return true;
    }

    return false;
}

std::string CharmMonitor::get_pet_owner_name(Zeal::EqStructures::Entity* pet)
{
    if (!pet || !pet->PetOwnerSpawnId)
        return "";

    Zeal::EqStructures::Entity* owner = Zeal::EqGame::get_entity_by_id(pet->PetOwnerSpawnId);
    if (!owner)
        return "";

    return owner->Name;
}

void CharmMonitor::log_charm_break(const std::string& pet_name, const std::string& owner_name)
{
    // Check if this owner is in the ignored list
    if (is_owner_ignored(owner_name)) {
        // Skip notification for ignored owners
        return;
    }

    // Format the message
    std::string message = pet_name + " has broken charm! (" + owner_name + "'s pet)";

    // Log to the game log
    Zeal::EqGame::log(message);

    // Also print to chat for immediate visibility
    Zeal::EqGame::print_chat(USERCOLOR_SPELL_FAILURE, "%s", message.c_str());
}

void CharmMonitor::toggle_owner_notification(const std::string& owner_name)
{
    if (owner_name.empty()) {
        Zeal::EqGame::print_chat("Invalid owner name");
        return;
    }

    // Check if the owner is already in the ignored list
    bool was_ignored = is_owner_ignored(owner_name);

    if (was_ignored) {
        // Remove from ignored list
        ignored_owners_set.erase(owner_name);
        Zeal::EqGame::print_chat("Charm break notifications for %s's pets are now ENABLED", owner_name.c_str());
    } else {
        // Add to ignored list
        ignored_owners_set.insert(owner_name);
        Zeal::EqGame::print_chat("Charm break notifications for %s's pets are now DISABLED", owner_name.c_str());
    }

    // Save the updated list
    save_ignored_owners();
}

void CharmMonitor::load_ignored_owners()
{
    // Clear the current set
    ignored_owners_set.clear();

    // Get the saved string from settings
    std::string saved_list = ignored_owners.get();
    if (saved_list.empty()) {
        return;
    }

    // Parse the comma-separated list
    size_t pos = 0;
    std::string token;
    while ((pos = saved_list.find(',')) != std::string::npos) {
        token = saved_list.substr(0, pos);
        if (!token.empty()) {
            ignored_owners_set.insert(token);
        }
        saved_list.erase(0, pos + 1);
    }

    // Add the last token if it exists
    if (!saved_list.empty()) {
        ignored_owners_set.insert(saved_list);
    }
}

void CharmMonitor::save_ignored_owners()
{
    // Convert the set to a comma-separated string
    std::string saved_list;
    for (const auto& owner : ignored_owners_set) {
        if (!saved_list.empty()) {
            saved_list += ",";
        }
        saved_list += owner;
    }

    // Save to settings
    ignored_owners.set(saved_list);
}

bool CharmMonitor::is_owner_ignored(const std::string& owner_name)
{
    return ignored_owners_set.find(owner_name) != ignored_owners_set.end();
}

void CharmMonitor::update_charm_pets()
{
    // Create a set of current charm pets to detect removed ones
    std::unordered_map<short, std::pair<std::string, std::string>> current_charm_pets;

    // Scan the entity list for charm pets
    Zeal::EqStructures::Entity* current = Zeal::EqGame::get_entity_list();
    while (current != nullptr) {
        if (is_charm_pet(current)) {
            std::string owner_name = get_pet_owner_name(current);
            current_charm_pets[current->SpawnId] = {current->Name, owner_name};
        }
        current = current->Next;
    }

    // Check for charm breaks (pets that were in charm_pets but not in current_charm_pets)
    for (const auto& [spawn_id, pet_info] : charm_pets) {
        if (current_charm_pets.find(spawn_id) == current_charm_pets.end()) {
            // This pet is no longer in the list, charm may have broken
            // Get the entity by ID to check if it still exists but is no longer charmed
            Zeal::EqStructures::Entity* entity = Zeal::EqGame::get_entity_by_id(spawn_id);
            if (entity && entity->Type == Zeal::EqEnums::EntityTypes::NPC) {
                // The entity still exists but is no longer a charm pet
                log_charm_break(pet_info.first, pet_info.second);
                // Store the spawn ID of the broken charm pet
                last_broken_charm_id = spawn_id;
            }
        }
    }

    // Update the charm_pets map for the next check
    charm_pets = current_charm_pets;
}

void CharmMonitor::list_group_pets()
{
    if (!Zeal::EqGame::is_in_game()) {
        Zeal::EqGame::print_chat("You must be in game to use this command");
        return;
    }

    // Get group info
    const Zeal::EqStructures::GroupInfo* group_info = Zeal::EqGame::GroupInfo;
    if (!group_info->is_in_group()) {
        Zeal::EqGame::print_chat("You are not in a group");
        return;
    }

    Zeal::EqGame::print_chat("Checking for pets owned by group members...");

    // Track if we found any pets
    bool found_pets = false;

    // Check each group member
    for (int i = 0; i < EQ_NUM_GROUP_MEMBERS; i++) {
        Zeal::EqStructures::Entity* group_member = group_info->EntityList[i];
        if (!group_member)
            continue;

        // Skip self
        if (group_member == Zeal::EqGame::get_self())
            continue;

        // Check if this group member has a pet
        bool member_has_pet = false;

        // Scan the entity list for pets owned by this group member
        Zeal::EqStructures::Entity* current = Zeal::EqGame::get_entity_list();
        while (current != nullptr) {
            if (current->Type == Zeal::EqEnums::EntityTypes::NPC &&
                current->PetOwnerSpawnId == group_member->SpawnId) {
                // Found a pet owned by this group member
                if (!member_has_pet) {
                    Zeal::EqGame::print_chat("%s's pets:", group_member->Name);
                    member_has_pet = true;
                    found_pets = true;
                }

                // Check if it's a charm pet
                bool is_charmed = is_charm_pet(current);

                // Print pet info
                Zeal::EqGame::print_chat("  - %s (ID: %d)%s",
                    current->Name,
                    current->SpawnId,
                    is_charmed ? " [CHARMED]" : "");
            }
            current = current->Next;
        }

        if (!member_has_pet) {
            Zeal::EqGame::print_chat("%s has no pets", group_member->Name);
        }
    }

    if (!found_pets) {
        Zeal::EqGame::print_chat("No pets found in your group");
    }
}

void CharmMonitor::target_last_broken_charm()
{
    if (!Zeal::EqGame::is_in_game()) {
        Zeal::EqGame::print_chat("You must be in game to use this command");
        return;
    }

    if (last_broken_charm_id == 0) {
        Zeal::EqGame::print_chat("No recently broken charm pets to target");
        return;
    }

    Zeal::EqStructures::Entity* entity = Zeal::EqGame::get_entity_by_id(last_broken_charm_id);
    if (entity && entity->Type == Zeal::EqEnums::EntityTypes::NPC) {
        Zeal::EqGame::set_target(entity);
        Zeal::EqGame::print_chat("Targeting broken charm pet: %s", entity->Name);
    } else {
        Zeal::EqGame::print_chat("Previously broken charm pet is no longer available");
        last_broken_charm_id = 0;
    }
}
