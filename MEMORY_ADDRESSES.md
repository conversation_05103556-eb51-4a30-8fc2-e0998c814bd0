# Finding EverQuest Memory Addresses

This document provides guidance on how to find the memory addresses needed for the EQ Map Tracker application to work with your specific EverQuest client version.

## Required Memory Addresses

To make the EQ Map Tracker fully functional, you need to find the following memory addresses:

1. **Zone Name Address**: The memory location that stores the current zone name
2. **Player Position Address**: The memory locations that store the player's X, Y, and Z coordinates
3. **Entity List Address**: The memory location that points to the start of the entity list
4. **Entity Structure**: The offsets within an entity structure for:
   - Entity ID
   - Entity Name
   - Entity Level
   - Entity Type (NPC, Player, etc.)
   - Entity Position (X, Y, Z)
   - Next Entity Pointer

## Tools for Finding Memory Addresses

### Recommended Tools

1. **Cheat Engine**: A memory scanning tool that can help find and monitor memory addresses
   - Download: [Cheat Engine](https://www.cheatengine.org/)

2. **Process Hacker**: A powerful process viewing and manipulation tool
   - Download: [Process Hacker](https://processhacker.sourceforge.io/)

3. **ReClass.NET**: A memory class reconstruction tool
   - Download: [ReClass.NET](https://github.com/ReClassNET/ReClass.NET)

## General Approach

### Finding the Zone Name

1. In Cheat Engine, attach to the EverQuest process
2. Use the "String scan" feature to search for the current zone name (e.g., "qeynos")
3. When you find matches, note the addresses
4. Zone to a different zone and repeat the search
5. Compare the results to identify the consistent address that changes with the zone

### Finding Player Position

1. In Cheat Engine, attach to the EverQuest process
2. Use the "Value scan" feature to search for your current X coordinate (use `/loc` in-game)
3. Move slightly and scan for the new value
4. Repeat until you narrow down to a few addresses
5. Test each address by monitoring it while moving in-game
6. Once you find the X coordinate, the Y and Z are typically adjacent in memory

### Finding Entity List

1. Find your player's entity address first (it's usually referenced by a static pointer)
2. Look for a linked list structure where each entity points to the next
3. Verify by checking that the list contains all visible entities in the zone
4. Map out the structure of an entity by examining the memory around a known entity address

## Updating the Code

Once you have found the necessary memory addresses, update the following functions in the `MemoryReader` class:

```python
def get_current_zone(self):
    # Replace with actual memory address
    zone_address = self.base_address + ZONE_OFFSET
    return self.read_string(zone_address)

def get_player_position(self):
    # Replace with actual memory addresses
    pos_x_address = self.base_address + PLAYER_X_OFFSET
    pos_y_address = self.base_address + PLAYER_Y_OFFSET
    pos_z_address = self.base_address + PLAYER_Z_OFFSET
    x = self.read_memory(pos_x_address, 'f')
    y = self.read_memory(pos_y_address, 'f')
    z = self.read_memory(pos_z_address, 'f')
    return Position(x, y, z)

def get_entities(self):
    entities = []
    # Replace with actual memory address
    entity_list_address = self.base_address + ENTITY_LIST_OFFSET
    current_entity = self.read_memory(entity_list_address, 'I')
    
    while current_entity:
        # Replace with actual offsets within the entity structure
        name = self.read_string(current_entity + NAME_OFFSET)
        level = self.read_memory(current_entity + LEVEL_OFFSET, 'I')
        x = self.read_memory(current_entity + X_OFFSET, 'f')
        y = self.read_memory(current_entity + Y_OFFSET, 'f')
        z = self.read_memory(current_entity + Z_OFFSET, 'f')
        entity_type = self.read_memory(current_entity + TYPE_OFFSET, 'I')
        
        entity = Entity(
            id=current_entity,
            name=name,
            level=level,
            position=Position(x, y, z),
            type='npc' if entity_type == NPC_TYPE else 'player'
        )
        entities.append(entity)
        
        # Move to the next entity in the linked list
        current_entity = self.read_memory(current_entity + NEXT_ENTITY_OFFSET, 'I')
    
    return entities
```

## Important Notes

- Memory addresses can change with each client update, so you may need to repeat this process after patches
- Using memory reading tools may violate the EverQuest Terms of Service
- This application is for educational purposes only
- Always use at your own risk
