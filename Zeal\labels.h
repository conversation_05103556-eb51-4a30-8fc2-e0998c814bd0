#pragma once
#include "hook_wrapper.h"
#include "memory.h"
#include <vector>
#include <chrono>

struct ManaUptick {
	std::chrono::steady_clock::time_point timestamp;
	int mana_gained;
	int mana_at_time;
};

class Labels
{
public:
	std::string debug_info;
	void print_debug_info(std::string);
	void print_debug_info(const char* format, ...);
	bool GetLabel(int EqType, std::string& str);
	int GetGauge(int EqType, std::string& str);
	Labels(class ZealService* zeal);
	~Labels();
	void callback_main();
private:
	// Mana tracking for time-to-full calculation
	int last_mana = -1;
	std::chrono::steady_clock::time_point last_mana_check;
	std::vector<ManaUptick> mana_upticks;

	void update_mana_tracking(int current_mana);
	int calculate_minutes_to_full(int current_mana, int max_mana);
};
