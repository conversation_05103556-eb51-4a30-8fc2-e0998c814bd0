<?xml version="1.0" encoding="us-ascii"?>
<XML ID="EQInterfaceDefinitionLanguage">
  <Schema xmlns="EverQuestData" xmlns:dt="EverQuestDataTypes" />
  <Button item="ZealDialogButton2">
    <ScreenID>ZealDialogButton2</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>198</X>
      <Y>64</Y>
    </Location>
    <Size>
      <CX>100</CX>
      <CY>24</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>false</Style_Checkbox>
    <Text>Button2</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
    <AutoStretch>false</AutoStretch>
    <TopAnchorToTop>false</TopAnchorToTop>
    <RightAnchorToLeft>false</RightAnchorToLeft>
    <LeftAnchorToLeft>false</LeftAnchorToLeft>
    <BottomAnchorToTop>false</BottomAnchorToTop>
    <BottomAnchorOffset>1</BottomAnchorOffset>
  </Button>
  <Button item="ZealDialogButton1">
    <ScreenID>ZealDialogButton1</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>90</X>
      <Y>64</Y>
    </Location>
    <Size>
      <CX>100</CX>
      <CY>24</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>false</Style_Checkbox>
    <Text>Button1</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
    <AutoStretch>false</AutoStretch>
    <TopAnchorToTop>false</TopAnchorToTop>
    <RightAnchorToLeft>false</RightAnchorToLeft>
    <LeftAnchorToLeft>false</LeftAnchorToLeft>
    <BottomAnchorToTop>false</BottomAnchorToTop>
    <BottomAnchorOffset>1</BottomAnchorOffset>
  </Button>
  <Label item="ZealDialogMessage">
    <ScreenID>ZealDialogMessage</ScreenID>
    <Font>3</Font>
    <Text>This is a really decent test of the length capacity of the message</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <Size>
      <CX>100</CX>
      <CY>80</CY>
    </Size>
    <AlignCenter>true</AlignCenter>
    <Location>
      <X>31</X>
      <Y>16</Y>
    </Location>
    <AutoStretch>true</AutoStretch>
    <RelativePosition>true</RelativePosition>
    <RightAnchorToLeft>false</RightAnchorToLeft>
    <TopAnchorToTop>true</TopAnchorToTop>
    <LeftAnchorToLeft>true</LeftAnchorToLeft>
    <BottomAnchorToTop>false</BottomAnchorToTop>
	<TopAnchorOffset>20</TopAnchorOffset>
    <BottomAnchorOffset>60</BottomAnchorOffset>
  </Label>
  <Editbox item="ZealDialogInput">
    <ScreenID>ZealDialogInput</ScreenID>
    <DrawTemplate>WDT_Inner</DrawTemplate>
    <RelativePosition>true</RelativePosition>
    <AutoStretch>true</AutoStretch>
    <LeftAnchorOffset>10</LeftAnchorOffset>
    <TopAnchorOffset>51</TopAnchorOffset>
    <RightAnchorOffset>10</RightAnchorOffset>
    <BottomAnchorOffset>30</BottomAnchorOffset>
    <TopAnchorToTop>false</TopAnchorToTop>
    <RightAnchorToLeft>false</RightAnchorToLeft>
    <BottomAnchorToTop>false</BottomAnchorToTop>
    <Style_Transparent>false</Style_Transparent>
    <Style_Border>true</Style_Border>
    <TextColor>
      <Alpha>255</Alpha>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
  </Editbox>
  <Screen item="ZealInputDialog">
    <RelativePosition>false</RelativePosition>
    <Location>
      <X>95</X>
      <Y>240</Y>
    </Location>
    <Size>
      <CX>421</CX>
      <CY>120</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <DrawTemplate>WDT_Def2</DrawTemplate>
    <Style_Titlebar>true</Style_Titlebar>
    <Style_Closebox>true</Style_Closebox>
    <Style_Minimizebox>false</Style_Minimizebox>
    <Style_Border>true</Style_Border>
    <Style_Sizable>false</Style_Sizable>
    <Pieces>ZealDialogInput</Pieces>
    <Pieces>ZealDialogMessage</Pieces>
    <Pieces>ZealDialogButton1</Pieces>
    <Pieces>ZealDialogButton2</Pieces>
  </Screen>
</XML>