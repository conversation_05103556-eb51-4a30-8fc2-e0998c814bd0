//------------------------------------------------------------------------------
// File: AMStream.idl
//
// Desc: 
//
// Copyright (c) 1998 - 2000, Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------------------------


import "unknwn.idl";
import "mmstream.idl";
import "strmif.idl";

cpp_quote("#include <ddraw.h>")
cpp_quote("#include <mmsystem.h>")
cpp_quote("#include <mmstream.h>")
cpp_quote("#include <ddstream.h>")
cpp_quote("#include <austream.h>")



interface IAMMultiMediaStream;
interface IAMMediaStream;
interface IMediaStreamFilter;
interface IDirectDraw;
interface IDirectDrawSurface;
interface IAMMediaTypeStream;
interface IAMMediaTypeSample;

//  Flags definitions for IAMMultiMediaStream::Initialize
enum {
    AMMSF_NOGRAPHTHREAD      = 0x00000001
};

//  Flags definitions for AddMediaStream and IAMMediaStream::Initialize
enum {
    //  Don't add a stream - create a default renderer instead
    //  for the supplied purpose id
    AMMSF_ADDDEFAULTRENDERER = 0x00000001,
    AMMSF_CREATEPEER         = 0x00000002,

    //  If no samples are created when we run or the last sample
    //  is deleted then terminate this stream
    AMMSF_STOPIFNOSAMPLES    = 0x00000004,

    //  If Update is not called keep going
    AMMSF_NOSTALL            = 0x00000008
};


//  Flag definitions for OpenFile and OpenMoniker
enum {
    AMMSF_RENDERTYPEMASK     = 0x00000003,
    AMMSF_RENDERTOEXISTING   = 0x00000000,
    AMMSF_RENDERALLSTREAMS   = 0x00000001,
    AMMSF_NORENDER           = 0x00000002,

    AMMSF_NOCLOCK            = 0x00000004,
    AMMSF_RUN                = 0x00000008
};


typedef [v1_enum] enum {
    Disabled = 0,
    ReadData = 1,
    RenderData = 2
} OUTPUT_STATE;


[
object,
uuid(7DB01C96-C0C3-11d0-8FF1-00C04FD9189D),
dual,
helpstring("IDirectShowStream Interface"),
pointer_default(unique)
]
interface IDirectShowStream : IDispatch
{
    [propget, id(1), helpstring("property FileName")] HRESULT FileName([out, retval] BSTR *pVal);
    [propput, id(1), helpstring("property FileName")] HRESULT FileName([in] BSTR newVal);
    [propget, id(2), helpstring("property Video")] HRESULT Video([out, retval] OUTPUT_STATE *pVal);
    [propput, id(2), helpstring("propetry Video")] HRESULT Video([in] OUTPUT_STATE newVal);
    [propget, id(3), helpstring("property Audio")] HRESULT Audio([out, retval] OUTPUT_STATE *pVal);
    [propput, id(3), helpstring("propetry Audio")] HRESULT Audio([in] OUTPUT_STATE newVal);
};


// IAMMultiMediaStream interface
[
object,
uuid(BEBE595C-9A6F-11d0-8FDE-00C04FD9189D),
pointer_default(unique)
]
interface IAMMultiMediaStream : IMultiMediaStream
{
	HRESULT Initialize(
		[in] STREAM_TYPE StreamType,
                [in] DWORD dwFlags,
		[in]  IGraphBuilder *pFilterGraph);

        HRESULT GetFilterGraph(
                [out] IGraphBuilder **ppGraphBuilder);

	HRESULT GetFilter(
		[out] IMediaStreamFilter **ppFilter);

	HRESULT AddMediaStream(
		[in]  IUnknown *pStreamObject,
		[in]  const MSPID *PurposeId,
		[in] DWORD dwFlags,
		[out]  IMediaStream **ppNewStream);

	HRESULT OpenFile(
		[in] LPCWSTR pszFileName,
		[in] DWORD dwFlags);

	HRESULT OpenMoniker(
                [in] IBindCtx *pCtx,
		[in] IMoniker *pMoniker,
		[in] DWORD dwFlags);

	HRESULT Render(
		[in] DWORD dwFlags);
}


// IAMMediaStream interface
[
object,
uuid(BEBE595D-9A6F-11d0-8FDE-00C04FD9189D),
pointer_default(unique)
]
interface IAMMediaStream : IMediaStream
{
	HRESULT Initialize(
                [in]  IUnknown *pSourceObject,
                [in] DWORD dwFlags,
		[in] REFMSPID PurposeId,
		[in] const STREAM_TYPE StreamType);

	HRESULT SetState(
		[in] FILTER_STATE State);

	HRESULT JoinAMMultiMediaStream(
		[in] IAMMultiMediaStream *pAMMultiMediaStream);

	HRESULT JoinFilter(
		[in] IMediaStreamFilter *pMediaStreamFilter);

	HRESULT JoinFilterGraph(
		[in] IFilterGraph *pFilterGraph);
};




// IMediaStreamFilter interface
[
object,
local,
uuid(BEBE595E-9A6F-11d0-8FDE-00C04FD9189D),
pointer_default(unique)
]
interface IMediaStreamFilter : IBaseFilter
{
	HRESULT AddMediaStream(
		[in] IAMMediaStream *pAMMediaStream);

	HRESULT GetMediaStream(
		[in] REFMSPID idPurpose,
		[out] IMediaStream **ppMediaStream);

	HRESULT EnumMediaStreams(
		[in] long Index,
		[out] IMediaStream **ppMediaStream);

        HRESULT SupportSeeking(
		[in] BOOL bRenderer);

	HRESULT ReferenceTimeToStreamTime(
		[in] [out] REFERENCE_TIME *pTime);

	HRESULT GetCurrentStreamTime(
		[out] REFERENCE_TIME *pCurrentStreamTime);

	HRESULT WaitUntil(
		[in]  REFERENCE_TIME WaitStreamTime);

	HRESULT Flush(
                [in] BOOL bCancelEOS);

        HRESULT EndOfStream();
};



[
object,
local,
uuid(AB6B4AFC-F6E4-11d0-900D-00C04FD9189D),
pointer_default(unique)
]
interface IDirectDrawMediaSampleAllocator : IUnknown
{
        HRESULT GetDirectDraw(IDirectDraw **ppDirectDraw);
};


[
object,
local,
uuid(AB6B4AFE-F6E4-11d0-900D-00C04FD9189D),
pointer_default(unique)
]
interface IDirectDrawMediaSample : IUnknown
{
        HRESULT GetSurfaceAndReleaseLock(
                [out] IDirectDrawSurface **ppDirectDrawSurface,
                [out] RECT * pRect);
        HRESULT LockMediaSamplePointer(void);
};







[
object,
local,
uuid(AB6B4AFA-F6E4-11d0-900D-00C04FD9189D),
pointer_default(unique)
]

interface IAMMediaTypeStream : IMediaStream
{
        HRESULT GetFormat(
                [out]   AM_MEDIA_TYPE * pMediaType,
                [in]    DWORD dwFlags);

        HRESULT SetFormat(
                [in]    AM_MEDIA_TYPE * pMediaType,
                [in]    DWORD dwFlags);

        HRESULT CreateSample(
                [in]            long lSampleSize,
                [in]  BYTE * pbBuffer,
                [in]            DWORD dwFlags,
                [in]  IUnknown *pUnkOuter,
                [out]           IAMMediaTypeSample ** ppAMMediaTypeSample);

        HRESULT GetStreamAllocatorRequirements(
                [out] ALLOCATOR_PROPERTIES *pProps);

        HRESULT SetStreamAllocatorRequirements(
                [in]  ALLOCATOR_PROPERTIES *pProps);
};


[
object,
local,
uuid(AB6B4AFB-F6E4-11d0-900D-00C04FD9189D),
pointer_default(unique)
]
interface IAMMediaTypeSample : IStreamSample
{
    //
    //  Unique methods for IAMMediaTypeSample
    //
    HRESULT SetPointer([in] BYTE *pBuffer, [in] long lSize);

    //
    //  Mirror of IMediaSample
    //
    HRESULT GetPointer([out] BYTE ** ppBuffer);
    long GetSize(void);
    HRESULT GetTime([out] REFERENCE_TIME * pTimeStart, [out] REFERENCE_TIME * pTimeEnd);
    HRESULT SetTime([in] REFERENCE_TIME * pTimeStart, [in] REFERENCE_TIME * pTimeEnd);
    HRESULT IsSyncPoint(void);
    HRESULT SetSyncPoint(BOOL bIsSyncPoint);
    HRESULT IsPreroll(void);
    HRESULT SetPreroll(BOOL bIsPreroll);
    long GetActualDataLength(void);
    HRESULT SetActualDataLength(long);
    HRESULT GetMediaType(AM_MEDIA_TYPE **ppMediaType);
    HRESULT SetMediaType(AM_MEDIA_TYPE *pMediaType);
    HRESULT IsDiscontinuity(void);
    HRESULT SetDiscontinuity(BOOL bDiscontinuity);
    HRESULT GetMediaTime([out] LONGLONG * pTimeStart, [out] LONGLONG * pTimeEnd);
    HRESULT SetMediaTime([in] LONGLONG * pTimeStart, [in] LONGLONG * pTimeEnd);
};


[
	uuid(4E6CDE29-C0C4-11d0-8FF1-00C04FD9189D),
	version(1.0),
	helpstring("DirectShowStream 1.0 Type Library")
]

library DirectShowStreamLib
{
	importlib("stdole32.tlb");
	importlib("stdole2.tlb");

	[
		uuid(49c47ce5-9ba4-11d0-8212-00c04fc32c45),
		helpstring("DirectShow Multi Media Stream")
	]
	coclass AMMultiMediaStream
	{
		[default] dispinterface IDirectShowStream;
	};
};



//
//  The MIDL compiler wants to produce a CLSID for everything defined in
//  our type library, but it also wants to generate huge, fat proxy code
//  so we use DEFINE_GUID for all other classes.  It has another interesting
//  bug in that it defines CLSID_AMMultiMediaStream within a #ifdef __cplusplus
//  block, so we need to define it outside of that scope.
//

cpp_quote("#ifndef __cplusplus")
cpp_quote("EXTERN_C const CLSID CLSID_AMMultiMediaStream;")
cpp_quote("#endif")

cpp_quote("DEFINE_GUID(CLSID_AMDirectDrawStream, /* 49c47ce4-9ba4-11d0-8212-00c04fc32c45 */")
cpp_quote("0x49c47ce4, 0x9ba4, 0x11d0, 0x82, 0x12, 0x00, 0xc0, 0x4f, 0xc3, 0x2c, 0x45);")
cpp_quote("DEFINE_GUID(CLSID_AMAudioStream, /* 8496e040-af4c-11d0-8212-00c04fc32c45 */")
cpp_quote("0x8496e040, 0xaf4c, 0x11d0, 0x82, 0x12, 0x00, 0xc0, 0x4f, 0xc3, 0x2c, 0x45);")
cpp_quote("DEFINE_GUID(CLSID_AMAudioData, /* f2468580-af8a-11d0-8212-00c04fc32c45 */")
cpp_quote("0xf2468580, 0xaf8a, 0x11d0, 0x82, 0x12, 0x00, 0xc0, 0x4f, 0xc3, 0x2c, 0x45);")
cpp_quote("DEFINE_GUID(CLSID_AMMediaTypeStream, /* CF0F2F7C-F7BF-11d0-900D-00C04FD9189D */")
cpp_quote("0xcf0f2f7c, 0xf7bf, 0x11d0, 0x90, 0xd, 0x0, 0xc0, 0x4f, 0xd9, 0x18, 0x9d);")
