P -856.0000, 15.0000, 0.0009, 255, 0, 0, 3, to_The_Lavastorm_Mountains
P -858.0000, 76.0000, 0.0000, 255, 0, 0, 2, Succor
P 157.0743, -119.1838, -1.4851, 240, 240, 240, 2, Crystal_Room
P 146.8909, 41.2319, 0.0009, 255, 255, 0, 2, TRAP:_Fake_Floor
P 86.3834, 35.7525, 0.8393, 240, 240, 0, 2, TRAP:_Fake_Floor
P -342.5305, -146.4895, 1.6608, 240, 240, 0, 2, Fake_Wall
P -216.8519, -263.6559, -13.9989, 255, 255, 255, 2, Drop_off
P -218.1969, -257.5909, -12.6879, 255, 255, 255, 2, <PERSON><PERSON>
P 82.5890, 146.0514, -26.9979, 240, 240, 0, 2, Locked_Door_(<PERSON><PERSON><PERSON>,<PERSON><PERSON>_40)
P 62.9551, 112.3044, -26.9979, 240, 240, 0, 2, Locked_<PERSON>_(<PERSON><PERSON><PERSON><PERSON>_<PERSON>,<PERSON><PERSON>_40)
P 91.5347, 62.5876, -27.9979, 240, 240, 0, 2, Locked_<PERSON>_(<PERSON>`s_Key<PERSON>,<PERSON><PERSON>_25)
P 145.0000, 72.0000, 0.0000, 240, 240, 0, 2, Locked_Door_(Guard`s_Keyring,Picklock_25)
P 42.8430, 91.1449, -26.9979, 240, 240, 0, 2, Locked_Door_(Shiny_Metal_<PERSON>,Picklock_40)
P 174.0000, 1.0000, 0.0000, 240, 240, 0, 2, <PERSON>d_Door_(Bloodstained_Key,Picklock_45)
P -105.0441, -304.9311, 1.5350, 240, 240, 0, 2, Locked_Door_(Golden_Crescent_Key,Picklock_50)
P 64.4086, -373.9593, 1.5327, 0, 0, 0, 2, Akksstaff
P -173.3031, -249.7934, -27.9979, 0, 0, 0, 2, Bonecracker
P 98.4543, 23.4267, -28.9979, 0, 0, 0, 2, Linara_Parlone
P -188.4549, -54.4969, -27.9989, 0, 0, 0, 2, Ogre_Captain
P -363.6443, 25.7106, 0.4037, 0, 0, 0, 2, Tentacle_Terror
P 208.9124, 37.5710, -13.9979, 127, 64, 0, 2, Drelzna_(Hunter)
P 50.1989, 161.8609, -26.7429, 127, 64, 0, 2, Ekeros_(Hunter)
P -104.5676, -350.7326, 0.0019, 127, 64, 0, 2, Najena_(Hunter)
P -234.2859, -90.7223, -26.4677, 127, 64, 0, 2, Officer_Grush_(Hunter)
P 115.5989, 154.4509, -26.9989, 127, 64, 0, 2, Rathyl_(Hunter)
P -217.4419, -357.1519, -13.9989, 127, 64, 0, 2, Trazdon_(Hunter)
P -220.7479, -462.4909, 0.0049, 127, 64, 0, 2, The_Widowmistress_(Hunter)
