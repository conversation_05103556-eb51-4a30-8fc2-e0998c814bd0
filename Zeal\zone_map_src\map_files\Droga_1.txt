P -300.0000, -1400.0000, 0.0000, 255, 0, 0, 3, to_Frontier_Mountains
P -247.7824, 1380.0861, -191.9354, 255, 0, 0, 3, to_The_Mines_of_Nurga
P 919.0000, 1319.0000, 0.0000, 255, 0, 0, 3, to_The_Mines_of_Nurga
P -290.0000, -1375.0000, 0.0000, 255, 0, 0, 2, Succor
P -1108.7170, 196.8150, -255.9050, 128, 0, 128, 2, Tinmizer`s_Stupendous_Contraption_(Pottery_Wheel)
P -1964.2874, -737.6108, -266.3684, 128, 0, 128, 2, Oven
P -1241.6650, 143.2144, -255.9041, 240, 240, 0, 2, Locked_<PERSON>_(<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>,Picklock_100)
P -1249.2343, 237.4981, -255.9041, 240, 240, 0, 2, Locked_<PERSON>_(<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>,<PERSON><PERSON>_100)
P -1277.5792, 215.1064, -255.9032, 240, 240, 0, 2, Locked_Door_(<PERSON><PERSON>_<PERSON><PERSON>,<PERSON><PERSON>_100)
P -1153.4235, 194.7359, -255.9032, 240, 240, 0, 2, <PERSON><PERSON>_<PERSON>_(<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>,<PERSON><PERSON>_100)
P -1237.8586, 180.5903, -255.9041, 240, 240, 0, 2, <PERSON>d_<PERSON>_(<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>,<PERSON><PERSON>_100)
P -316.7124, -1304.5088, 0.0020, 240, 240, 0, 2, T<PERSON><PERSON>:_F<PERSON>_<PERSON>
P 858.8414, 1188.8171, -78.4401, 240, 240, 0, 2, One_Way_Down
P -1718.8550, -476.6297, -254.3678, 240, 240, 0, 2, Fake_Wall
P -1768.5607, -472.1208, -254.3621, 240, 240, 0, 2, Fake_Wall
P -1254.6256, 250.9964, -143.9354, 240, 240, 0, 2, TRAP:_Pit
P -1211.8380, 263.0123, -255.9041, 240, 240, 0, 2, Fake_Wall
P -1234.8248, 263.5745, -255.9041, 240, 240, 0, 2, Fake_Wall
P -1064.2244, 334.3175, -175.9354, 240, 240, 0, 2, One-Way_(to_West)
P -1298.3007, 218.4263, -254.2913, 0, 128, 128, 2, Gillia_Brissok_(Quest)
P -1754.8248, -467.0322, -255.9041, 0, 0, 0, 2, Jeren_Manri
P -2195.2881, 419.3750, -351.8430, 0, 0, 0, 2, a_goblin_food_supplier
P -1184.7141, 263.9687, -254.2790, 0, 0, 0, 2, a_goblin_janitor_(Droga_Jail_Key)
P 928.9913, 1245.7615, -175.9354, 0, 0, 0, 2, a_goblin_master_alchemist
P -1084.8621, 58.8510, -255.9050, 0, 0, 0, 2, a_goblin_prison_warden_(Droga_Jail_Key)
P -1478.7380, -615.9320, -255.9040, 128, 64, 0, 2, Blood_Ritualist_Taprom_(Hunter)
P -227.0000, -367.0000, 0.0000, 128, 64, 0, 2, Bone_Caller_Nathid_(Hunter)
P -1457.8199, -460.8020, -255.9040, 128, 64, 0, 2, Bone_Caster_Fizzik_(Hunter)
P -1986.5430, -124.6460, -287.8730, 128, 64, 0, 2, Bone_Knight_Bunri_(Hunter)
P -1744.7170, -351.2720, -271.9040, 128, 64, 0, 2, Bone_Trooper_Grolin_(Hunter)
P -2075.3953, -260.5414, -287.8727, 127, 64, 0, 2, Cave_Guardian_Prehtil_(Hunter)
P -160.0000, 575.0000, 0.0000, 128, 64, 0, 2, Cave_Guardian_Prehtil_(Hunter)
P -1415.1610, -365.9990, -255.9040, 128, 64, 0, 2, Death_Ritualist_Stromik_(Hunter)
P -2008.2690, -125.7110, -287.8730, 128, 64, 0, 2, Dirt_Criminal_Vakov_(Hunter)
P -1475.8180, -617.3190, -255.9040, 128, 64, 0, 2, Dirt_Defender_Gropp_(Hunter)
P -1371.9821, -542.7980, -255.9040, 128, 64, 0, 2, Dust_Hoodlum_Heaprit_(Hunter)
P -1952.9019, -202.2755, -287.8728, 127, 64, 0, 2, Earth_Mystic_Gedak_(Hunter)
P 195.0000, 105.0000, 0.0000, 128, 64, 0, 2, Earth_Mystic_Gedak_(Hunter)
P -1944.0000, -629.0000, 50.0000, 128, 64, 0, 2, Earth_Seer_Gamolk_(Hunter)
P -2118.6960, 577.2060, -335.8730, 128, 64, 0, 2, a_fierce_drogan_spider_(Hunter)
P 954.0000, 363.0000, 0.0000, 128, 64, 0, 2, Flame_Adept_Verdalix_(Hunter)
P -1514.1604, -605.9421, -255.9042, 127, 64, 0, 2, Flame_Adept_Verdalix_(Hunter)
P -1608.0000, -126.0000, 0.0000, 128, 64, 0, 2, Flame_Master_Japal_(Hunter)
P -1441.8776, -508.4537, -255.9041, 127, 64, 0, 2, Flame_Master_Japal_(Hunter)
P -2052.8274, -259.9947, -287.8728, 127, 64, 0, 2, Ice_Adept_Amozik_(Hunter)
P -226.0000, 488.0000, 0.0000, 128, 64, 0, 2, Ice_Adept_Amozik_(Hunter)
P -1393.4989, -487.4630, -253.9818, 127, 64, 0, 2, Ice_Master_Cialin_(Hunter)
P -2055.5845, 484.8532, -351.8415, 128, 64, 0, 2, Ice_Master_Cialin_(Hunter)
P -1428.2520, -618.5372, -255.9041, 127, 64, 0, 2, Jailer_Maufan_(Hunter)
P -1053.0000, 2.0000, 0.0000, 128, 64, 0, 2, Jailor_Maufan_(Hunter)
P -1901.8538, -694.9269, -267.9041, 128, 64, 0, 2, King_Dronan_(Hunter)
P -1049.9596, 240.4679, -160.0973, 127, 64, 0, 2, King_Dronan_(Hunter)
P -781.5440, 685.8664, -190.4227, 127, 64, 0, 2, King_Dronan_(Hunter,Roam)
P 333.7271, -18.4263, -175.9354, 127, 64, 0, 2, King_Dronan_(Hunter)
P -1711.9242, -329.5887, -271.9041, 128, 64, 0, 2, Magic_Adept_Xabok_(Hunter)
P -1509.9680, -563.1590, -255.9040, 128, 64, 0, 2, Moss_Knight_Yortal_(Hunter)
P -1435.3051, -375.9070, -255.9040, 128, 64, 0, 2, Rock_Sneak_Nrillip_(Hunter)
P -1976.2950, -736.4280, -266.8740, 128, 64, 0, 2, a_savage_drogan_spider_(Hunter)
P -2161.6050, 60.1940, -335.8730, 128, 64, 0, 2, Slinker_Ukul_(Hunter)
P -1999.0574, 144.5241, -349.9064, 127, 64, 0, 2, Stalag_Trooper_Biama_(Hunter)
P 623.0000, 481.0000, 0.0000, 128, 64, 0, 2, Stalag_Trooper_Biamas_(Hunter)
P -193.0000, -92.0000, 0.0000, 128, 64, 0, 2, Stone_Crook_Piklo_(Hunter)
P -2185.4500, 253.8130, -335.8730, 128, 64, 0, 2, Stone_Seer_Kaigek_(Hunter)
P -1793.7025, -292.6518, -271.9041, 127, 64, 0, 2, Whip_Cracker_Krazzim_(Hunter)
P 181.0000, -2.0000, 0.0000, 128, 64, 0, 2, Whip_Cracker_Krazzim_(Hunter)