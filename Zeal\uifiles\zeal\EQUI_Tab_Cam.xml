<?xml version="1.0" encoding="us-ascii"?>
<XML ID="EQInterfaceDefinitionLanguage">
  <Schema xmlns="EverQuestData" xmlns:dt="EverQuestDataTypes" />

  <Button item="Zeal_Cam">
    <ScreenID>Zeal_Cam</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>10</X>
      <Y>14</Y>
    </Location>
    <Size>
      <CX>150</CX>
      <CY>20</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Toggles Zeal's mouse look smoothing</TooltipReference>
    <Text>Enabled</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
   <!-- Zeal Pan Delay slider -->
  <Label item="Zeal_PanDelayLabel">
    <ScreenID>Zeal_PanDelayLabel</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>197</X>
      <Y>10</Y>
    </Location>
    <Size>
      <CX>150</CX>
      <CY>14</CY>
    </Size>
    <Text>Pan Delay</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>false</AlignRight>
  </Label>
  <Slider item="Zeal_PanDelaySlider">
    <ScreenID>Zeal_PanDelaySlider</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>197</X>
      <Y>30</Y>
    </Location>
    <Size>
      <CX>100</CX>
      <CY>16</CY>
    </Size>
    <SliderArt>SDT_DefSlider</SliderArt>
  </Slider>
  <Label item="Zeal_PanDelayValueLabel">
    <ScreenID>Zeal_PanDelayValueLabel</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>297</X>
      <Y>30</Y>
    </Location>
    <Size>
      <CX>50</CX>
      <CY>16</CY>
    </Size>
    <Text>0.0</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>true</AlignRight>
  </Label>
  <!-- Zeal Cam Sensitivity -->
  <Label item="Zeal_CamSensitivityLabel">
    <ScreenID>Zeal_CamSensitivityLabel</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>200</X>
      <Y>101</Y>
    </Location>
    <Size>
      <CX>150</CX>
      <CY>14</CY>
    </Size>
    <Text>Zeal Cam Sensitivity</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>false</AlignRight>
  </Label>
  <!-- First Person X -->
  <Label item="Zeal_FirstPersonXLabel">
    <ScreenID>Zeal_FirstPersonXLabel</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>220</X>
      <Y>125</Y>
    </Location>
    <Size>
      <CX>150</CX>
      <CY>14</CY>
    </Size>
    <Text>First Person X</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>false</AlignRight>
  </Label>
  <Slider item="Zeal_FirstPersonSlider_X">
    <ScreenID>Zeal_FirstPersonSlider_X</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>220</X>
      <Y>145</Y>
    </Location>
    <Size>
      <CX>100</CX>
      <CY>16</CY>
    </Size>
    <SliderArt>SDT_DefSlider</SliderArt>
  </Slider>
  <Label item="Zeal_FirstPersonLabel_X">
    <ScreenID>Zeal_FirstPersonLabel_X</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>310</X>
      <Y>144</Y>
    </Location>
    <Size>
      <CX>50</CX>
      <CY>16</CY>
    </Size>
    <Text>0.0</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>true</AlignRight>
  </Label>
  <!-- First Person Y -->
  <Label item="Zeal_FirstPersonYLabel">
    <ScreenID>Zeal_FirstPersonYLabel</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>220</X>
      <Y>163</Y>
    </Location>
    <Size>
      <CX>150</CX>
      <CY>14</CY>
    </Size>
    <Text>First Person Y</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>false</AlignRight>
  </Label>
  <Slider item="Zeal_FirstPersonSlider_Y">
    <ScreenID>Zeal_FirstPersonSlider_Y</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>220</X>
      <Y>183</Y>
    </Location>
    <Size>
      <CX>100</CX>
      <CY>16</CY>
    </Size>
    <SliderArt>SDT_DefSlider</SliderArt>
  </Slider>
  <Label item="Zeal_FirstPersonLabel_Y">
    <ScreenID>Zeal_FirstPersonLabel_Y</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>310</X>
      <Y>182</Y>
    </Location>
    <Size>
      <CX>50</CX>
      <CY>16</CY>
    </Size>
    <Text>0.0</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>true</AlignRight>
  </Label>
  <!-- Third Person X -->
  <Label item="Zeal_ThirdPersonXLabel">
    <ScreenID>Zeal_ThirdPersonXLabel</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>220</X>
      <Y>204</Y>
    </Location>
    <Size>
      <CX>150</CX>
      <CY>14</CY>
    </Size>
    <Text>Third Person X</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>false</AlignRight>
  </Label>
  <Slider item="Zeal_ThirdPersonSlider_X">
    <ScreenID>Zeal_ThirdPersonSlider_X</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>220</X>
      <Y>224</Y>
    </Location>
    <Size>
      <CX>100</CX>
      <CY>16</CY>
    </Size>
    <SliderArt>SDT_DefSlider</SliderArt>
  </Slider>
  <Label item="Zeal_ThirdPersonLabel_X">
    <ScreenID>Zeal_ThirdPersonLabel_X</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>310</X>
      <Y>223</Y>
    </Location>
    <Size>
      <CX>50</CX>
      <CY>16</CY>
    </Size>
    <Text>0.0</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>true</AlignRight>
  </Label>
  <!-- Third Person Y -->
  <Label item="Zeal_ThirdPersonYLabel">
    <ScreenID>Zeal_ThirdPersonYLabel</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>220</X>
      <Y>242</Y>
    </Location>
    <Size>
      <CX>150</CX>
      <CY>14</CY>
    </Size>
    <Text>Third Person Y</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>false</AlignRight>
  </Label>
  <Slider item="Zeal_ThirdPersonSlider_Y">
    <ScreenID>Zeal_ThirdPersonSlider_Y</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>220</X>
      <Y>262</Y>
    </Location>
    <Size>
      <CX>100</CX>
      <CY>16</CY>
    </Size>
    <SliderArt>SDT_DefSlider</SliderArt>
  </Slider>
  <Label item="Zeal_ThirdPersonLabel_Y">
    <ScreenID>Zeal_ThirdPersonLabel_Y</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>310</X>
      <Y>261</Y>
    </Location>
    <Size>
      <CX>50</CX>
      <CY>16</CY>
    </Size>
    <Text>0.0</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>true</AlignRight>
  </Label>
  <Label item="Zeal_FovLabel_">
    <ScreenID>Zeal_FovLabel_</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>197</X>
      <Y>52</Y>
    </Location>
    <Size>
      <CX>150</CX>
      <CY>14</CY>
    </Size>
    <Text>Field of View</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>false</AlignRight>
  </Label>
  <Slider item="Zeal_Fov">
    <ScreenID>Zeal_FoVSlider</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>197</X>
      <Y>72</Y>
    </Location>
    <Size>
      <CX>100</CX>
      <CY>16</CY>
    </Size>
    <SliderArt>SDT_DefSlider</SliderArt>
  </Slider>
  <Label item="Zeal_FovLabel">
    <ScreenID>Zeal_FoVValueLabel</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>297</X>
      <Y>72</Y>
    </Location>
    <Size>
      <CX>50</CX>
      <CY>16</CY>
    </Size>
    <Text>0</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>true</AlignRight>
  </Label>
  <!-- Tooltip Hover Timeout -->
  <Label item="Zeal_HoverTimeout_Label">
    <ScreenID>Zeal_HoverTimeout_Label</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>197</X>
      <Y>26</Y>
    </Location>
    <Size>
      <CX>150</CX>
      <CY>14</CY>
    </Size>
    <Text>Tooltip Hover Delay</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>false</AlignRight>
  </Label>
  <Slider item="Zeal_HoverTimeout_Slider">
    <ScreenID>Zeal_HoverTimeout_Slider</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>197</X>
      <Y>46</Y>
    </Location>
    <Size>
      <CX>100</CX>
      <CY>16</CY>
    </Size>
    <SliderArt>SDT_DefSlider</SliderArt>
  </Slider>
  <Label item="Zeal_HoverTimeout_Value">
    <ScreenID>Zeal_HoverTimeout_Value</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>297</X>
      <Y>46</Y>
    </Location>
    <Size>
      <CX>50</CX>
      <CY>16</CY>
    </Size>
    <Text>0</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>true</AlignRight>
  </Label>
  <Button item="Zeal_UseOldSens">
    <ScreenID>Zeal_UseOldSens</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>10</X>
      <Y>36</Y>
    </Location>
    <Size>
      <CX>150</CX>
      <CY>20</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Uses the previous versions sensitivity adjustments for mouse look</TooltipReference>
    <Text>Old Sensitivity Math</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
   <Button item="Zeal_Cam_TurnLocked">
    <ScreenID>Zeal_Cam_TurnLocked</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>10</X>
      <Y>58</Y>
    </Location>
    <Size>
      <CX>150</CX>
      <CY>20</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Turns your camera in third person with turn keys</TooltipReference>
    <Text>Key Turn->Camera Turn</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <Page item="Tab_Camera">
    <ScreenID>Tab_Camera</ScreenID>
    <RelativePosition>true</RelativePosition>
    <!-- Pages are sized and positioned by their parent tab -->
    <Style_VScroll>true</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <DrawTemplate>WDT_Def</DrawTemplate>
    <TabText>Cam</TabText>
    <TabTextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TabTextColor>
    <TabTextActiveColor>
      <R>255</R>
      <G>255</G>
      <B>0</B>
    </TabTextActiveColor>
    <Style_Sizable>true</Style_Sizable>
    <Pieces>Zeal_Cam</Pieces>
    <Pieces>Zeal_UseOldSens</Pieces>
    <Pieces>Zeal_CamSensitivityLabel</Pieces>
    <Pieces>Zeal_FirstPersonXLabel</Pieces>
    <Pieces>Zeal_FirstPersonSlider_X</Pieces>
    <Pieces>Zeal_FirstPersonLabel_X</Pieces>
    <Pieces>Zeal_FirstPersonYLabel</Pieces>
    <Pieces>Zeal_FirstPersonSlider_Y</Pieces>
    <Pieces>Zeal_FirstPersonLabel_Y</Pieces>
    <Pieces>Zeal_ThirdPersonXLabel</Pieces>
    <Pieces>Zeal_ThirdPersonSlider_X</Pieces>
    <Pieces>Zeal_ThirdPersonLabel_X</Pieces>
    <Pieces>Zeal_ThirdPersonYLabel</Pieces>
    <Pieces>Zeal_ThirdPersonSlider_Y</Pieces>
    <Pieces>Zeal_ThirdPersonLabel_Y</Pieces>
    <Pieces>Zeal_PanDelayLabel</Pieces>
    <Pieces>Zeal_PanDelaySlider</Pieces>
    <Pieces>Zeal_PanDelayValueLabel</Pieces>
    <Pieces>Zeal_Fov</Pieces>
    <Pieces>Zeal_FovLabel</Pieces>
    <Pieces>Zeal_FovLabel_</Pieces>
	<Pieces>Zeal_Cam_TurnLocked</Pieces>
    <Location>
      <X>0</X>
      <Y>22</Y>
    </Location>
    <Size>
      <CX>380</CX>
      <CY>339</CY>
    </Size>
  </Page>
  </XML>