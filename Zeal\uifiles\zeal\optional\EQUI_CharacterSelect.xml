<?xml version = "1.0"?>
<XML ID = "EQInterfaceDefinitionLanguage">
	<Schema xmlns = "EverQuestData" xmlns:dt = "EverQuestDataTypes"/>

	<Button item = "CSW_Explore_Button">
		<ScreenID>Explore_Button</ScreenID>
		<!--<Font>3</Font>-->
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>2</X>
			<Y>60</Y>
		</Location>
		<Size>
			<CX>48</CX>
			<CY>32</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<!--<TooltipReference/>-->
		<Style_Checkbox>false</Style_Checkbox>
		<!--<RadioGroup/>-->
		<Text>Explore</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_SmallBtnNormal</Normal>
			<Pressed>A_SmallBtnPressed</Pressed>
			<Flyby>A_SmallBtnFlyby</Flyby>
			<Disabled>A_SmallBtnDisabled</Disabled>
			<PressedFlyby>A_SmallBtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item = "Zeal_ZoneSelect">
		<ScreenID>Zeal_ZoneSelect</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>70</X>
			<Y>60</Y>
		</Location>
		<Size>
			<CX>78</CX>
			<CY>32</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>Zone Select</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_SmallBtnNormal</Normal>
			<Pressed>A_SmallBtnPressed</Pressed>
			<Flyby>A_SmallBtnFlyby</Flyby>
			<Disabled>A_SmallBtnDisabled</Disabled>
			<PressedFlyby>A_SmallBtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item = "CSW_Rotate_Button">
		<ScreenID>Rotate_Button</ScreenID>
		<!--<Font>3</Font>-->
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>168</X>
			<Y>60</Y>
		</Location>
		<Size>
			<CX>48</CX>
			<CY>32</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<!--<TooltipReference/>-->
		<Style_Checkbox>true</Style_Checkbox>
		<!--<RadioGroup/>-->
		<Text>Rotate</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_SmallBtnNormal</Normal>
			<Pressed>A_SmallBtnPressed</Pressed>
			<Flyby>A_SmallBtnFlyby</Flyby>
			<Disabled>A_SmallBtnDisabled</Disabled>
			<PressedFlyby>A_SmallBtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>

	<Button item = "CSW_Char1_Button">
		<ScreenID>Char1_Button</ScreenID>
		<!--<Font>3</Font>-->
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>5</X>
			<Y>105</Y>
		</Location>
		<Size>
			<CX>210</CX>
			<CY>32</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<!--<TooltipReference/>-->
		<Style_Checkbox>true</Style_Checkbox>
		<RadioGroup>CSW_Radios</RadioGroup>
		<Text>Create New Character</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BigBtnNormal</Normal>
			<Pressed>A_BigBtnPressed</Pressed>
			<Flyby>A_BigBtnFlyby</Flyby>
			<Disabled>A_BigBtnDisabled</Disabled>
			<PressedFlyby>A_BigBtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>

	<Button item = "CSW_Char2_Button">
		<ScreenID>Char2_Button</ScreenID>
		<!--<Font>3</Font>-->
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>5</X>
			<Y>140</Y>
		</Location>
		<Size>
			<CX>210</CX>
			<CY>32</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<!--<TooltipReference/>-->
		<Style_Checkbox>true</Style_Checkbox>
		<RadioGroup>CSW_Radios</RadioGroup>
		<Text>Create New Character</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BigBtnNormal</Normal>
			<Pressed>A_BigBtnPressed</Pressed>
			<Flyby>A_BigBtnFlyby</Flyby>
			<Disabled>A_BigBtnDisabled</Disabled>
			<PressedFlyby>A_BigBtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>

	<Button item = "CSW_Char3_Button">
		<ScreenID>Char3_Button</ScreenID>
		<!--<Font>3</Font>-->
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>5</X>
			<Y>175</Y>
		</Location>
		<Size>
			<CX>210</CX>
			<CY>32</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<!--<TooltipReference/>-->
		<Style_Checkbox>true</Style_Checkbox>
		<RadioGroup>CSW_Radios</RadioGroup>
		<Text>Create New Character</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BigBtnNormal</Normal>
			<Pressed>A_BigBtnPressed</Pressed>
			<Flyby>A_BigBtnFlyby</Flyby>
			<Disabled>A_BigBtnDisabled</Disabled>
			<PressedFlyby>A_BigBtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>

	<Button item = "CSW_Char4_Button">
		<ScreenID>Char4_Button</ScreenID>
		<!--<Font>3</Font>-->
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>5</X>
			<Y>210</Y>
		</Location>
		<Size>
			<CX>210</CX>
			<CY>32</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<!--<TooltipReference/>-->
		<Style_Checkbox>true</Style_Checkbox>
		<RadioGroup>CSW_Radios</RadioGroup>
		<Text>Create New Character</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BigBtnNormal</Normal>
			<Pressed>A_BigBtnPressed</Pressed>
			<Flyby>A_BigBtnFlyby</Flyby>
			<Disabled>A_BigBtnDisabled</Disabled>
			<PressedFlyby>A_BigBtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>

	<Button item = "CSW_Char5_Button">
		<ScreenID>Char5_Button</ScreenID>
		<!--<Font>3</Font>-->
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>5</X>
			<Y>245</Y>
		</Location>
		<Size>
			<CX>210</CX>
			<CY>32</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<!--<TooltipReference/>-->
		<Style_Checkbox>true</Style_Checkbox>
		<RadioGroup>CSW_Radios</RadioGroup>
		<Text>Create New Character</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BigBtnNormal</Normal>
			<Pressed>A_BigBtnPressed</Pressed>
			<Flyby>A_BigBtnFlyby</Flyby>
			<Disabled>A_BigBtnDisabled</Disabled>
			<PressedFlyby>A_BigBtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>

	<Button item = "CSW_Char6_Button">
		<ScreenID>Char6_Button</ScreenID>
		<!--<Font>3</Font>-->
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>5</X>
			<Y>280</Y>
		</Location>
		<Size>
			<CX>210</CX>
			<CY>32</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<!--<TooltipReference/>-->
		<Style_Checkbox>true</Style_Checkbox>
		<RadioGroup>CSW_Radios</RadioGroup>
		<Text>Create New Character</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BigBtnNormal</Normal>
			<Pressed>A_BigBtnPressed</Pressed>
			<Flyby>A_BigBtnFlyby</Flyby>
			<Disabled>A_BigBtnDisabled</Disabled>
			<PressedFlyby>A_BigBtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>

	<Button item = "CSW_Char7_Button">
		<ScreenID>Char7_Button</ScreenID>
		<!--<Font>3</Font>-->
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>5</X>
			<Y>315</Y>
		</Location>
		<Size>
			<CX>210</CX>
			<CY>32</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<!--<TooltipReference/>-->
		<Style_Checkbox>true</Style_Checkbox>
		<RadioGroup>CSW_Radios</RadioGroup>
		<Text>Create New Character</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BigBtnNormal</Normal>
			<Pressed>A_BigBtnPressed</Pressed>
			<Flyby>A_BigBtnFlyby</Flyby>
			<Disabled>A_BigBtnDisabled</Disabled>
			<PressedFlyby>A_BigBtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>

	<Button item = "CSW_Char8_Button">
		<ScreenID>Char8_Button</ScreenID>
		<!--<Font>3</Font>-->
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>5</X>
			<Y>350</Y>
		</Location>
		<Size>
			<CX>210</CX>
			<CY>32</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<!--<TooltipReference/>-->
		<Style_Checkbox>true</Style_Checkbox>
		<RadioGroup>CSW_Radios</RadioGroup>
		<Text>Create New Character</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BigBtnNormal</Normal>
			<Pressed>A_BigBtnPressed</Pressed>
			<Flyby>A_BigBtnFlyby</Flyby>
			<Disabled>A_BigBtnDisabled</Disabled>
			<PressedFlyby>A_BigBtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>

	<Button item = "CSW_Enter_World_Button">
		<ScreenID>Enter_World_Button</ScreenID>
		<!--<Font>3</Font>-->
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>2</X>
			<Y>400</Y>
		</Location>
		<Size>
			<CX>80</CX>
			<CY>32</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<!--<TooltipReference/>-->
		<Style_Checkbox>false</Style_Checkbox>
		<!--<RadioGroup/>-->
		<Text>Enter World</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_SmallBtnNormal</Normal>
			<Pressed>A_SmallBtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_SmallBtnDisabled</Disabled>
			<PressedFlyby>A_SmallBtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>

	<Button item = "CSW_Delete_Button">
		<ScreenID>Delete_Button</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>100</X>
			<Y>400</Y>
		</Location>
		<Size>
			<CX>48</CX>
			<CY>32</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>Delete</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_SmallBtnNormal</Normal>
			<Pressed>A_SmallBtnPressed</Pressed>
			<Flyby>A_SmallBtnFlyby</Flyby>
			<Disabled>A_SmallBtnDisabled</Disabled>
			<PressedFlyby>A_SmallBtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item = "CSW_Quit_Button">
		<ScreenID>Quit_Button</ScreenID>
		<!--<Font>3</Font>-->
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>168</X>
			<Y>400</Y>
		</Location>
		<Size>
			<CX>48</CX>
			<CY>32</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<!--<TooltipReference/>-->
		<Style_Checkbox>false</Style_Checkbox>
		<!--<RadioGroup/>-->
		<Text>Quit</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_SmallBtnNormal</Normal>
			<Pressed>A_SmallBtnPressed</Pressed>
			<Flyby>A_SmallBtnFlyby</Flyby>
			<Disabled>A_SmallBtnDisabled</Disabled>
			<PressedFlyby>A_SmallBtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>

<!-- Fansy Title Graphic -->
	<StaticAnimation item = "CS_LeftTitlePiece">
		<ScreenID>CS_LeftTitlePiece</ScreenID>
		<Location>
			<X>17</X>
			<Y>0</Y>
		</Location>
		<Size>
			<CX>48</CX>
			<CY>56</CY>
		</Size>
		<Animation>A_LeftTitlePiece</Animation>
	</StaticAnimation>
	<StaticAnimation item = "CS_LeftCenterTitlePiece">
		<ScreenID>CS_LeftCenterTitlePiece</ScreenID>
		<Location>
			<X>65</X>
			<Y>0</Y>
		</Location>
		<Size>
			<CX>10</CX>
			<CY>56</CY>
		</Size>
		<Animation>A_StretchyTitlePiece</Animation>
	</StaticAnimation>
	<StaticAnimation item = "CS_MiddleTitlePiece">
		<ScreenID>CS_MiddleTitlePiece</ScreenID>
		<Location>
			<X>75</X>
			<Y>0</Y>
		</Location>
		<Size>
			<CX>66</CX>
			<CY>56</CY>
		</Size>
		<Animation>A_MiddleTitlePiece</Animation>
	</StaticAnimation>
	<StaticAnimation item = "CS_RightCenterTitlePiece">
		<ScreenID>CS_RightCenterTitlePiece</ScreenID>
		<Location>
			<X>141</X>
			<Y>0</Y>
		</Location>
		<Size>
			<CX>10</CX>
			<CY>56</CY>
		</Size>
		<Animation>A_StretchyTitlePiece</Animation>
	</StaticAnimation>
	<StaticAnimation item = "CS_RightTitlePiece">
		<ScreenID>CS_RightTitlePiece</ScreenID>
		<Location>
			<X>151</X>
			<Y>0</Y>
		</Location>
		<Size>
			<CX>48</CX>
			<CY>56</CY>
		</Size>
		<Animation>A_RightTitlePiece</Animation>
	</StaticAnimation>
	<Label item = "CS_TitleLabel">
		<ScreenID>CS_TitleLabel</ScreenID>
		<Font>4</Font>
		<Location>
			<X>17</X>
			<Y>20</Y>
		</Location>
		<Size>
			<CX>182</CX>
			<CY>15</CY>
		</Size>
		<Text>Character Select</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<AlignCenter>true</AlignCenter>
	</Label>

	<Screen item = "CharacterSelectWindow">
		<!--<ScreenID/>-->
		<!--<Font/>-->
		<RelativePosition>false</RelativePosition>
		<Location>
			<X>0</X>
			<Y>20</Y>
		</Location>
		<Size>
			<CX>228</CX>
			<CY>444</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<!--<TooltipReference/>-->
		<DrawTemplate>WDT_RoundedNoTitle</DrawTemplate>
		<Style_Titlebar>false</Style_Titlebar>
		<Style_Closebox>false</Style_Closebox>
		<Style_Minimizebox>false</Style_Minimizebox>
		<Style_Border>true</Style_Border>
		<Style_Sizable>false</Style_Sizable>
		<Pieces>CSW_Explore_Button</Pieces>
		<Pieces>CSW_Rotate_Button</Pieces>
		<Pieces>CSW_Char1_Button</Pieces>
		<Pieces>CSW_Char2_Button</Pieces>
		<Pieces>CSW_Char3_Button</Pieces>
		<Pieces>CSW_Char4_Button</Pieces>
		<Pieces>CSW_Char5_Button</Pieces>
		<Pieces>CSW_Char6_Button</Pieces>
		<Pieces>CSW_Char7_Button</Pieces>
		<Pieces>CSW_Char8_Button</Pieces>
		<Pieces>CSW_Enter_World_Button</Pieces>
		<Pieces>Zeal_ZoneSelect</Pieces>
		<Pieces>CSW_Delete_Button</Pieces>
		<Pieces>CSW_Quit_Button</Pieces>
		<Pieces>CS_LeftTitlePiece</Pieces>
		<Pieces>CS_RightTitlePiece</Pieces>
		<Pieces>CS_MiddleTitlePiece</Pieces>
		<Pieces>CS_LeftCenterTitlePiece</Pieces>
		<Pieces>CS_RightCenterTitlePiece</Pieces>
		<Pieces>CS_TitleLabel</Pieces>
	</Screen>


	<Label item = "EXW_CharInfoLabel">
		<ScreenID>EXW_CharInfoLabel</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>5</X>
			<Y>2</Y>
		</Location>
		<Size>
			<CX>390</CX>
			<CY>15</CY>
		</Size>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<AlignCenter>true</AlignCenter>
	</Label>

	<Screen item = "EXW_ExploreModeWindow2">
		<ScreenID>EXW_ExploreModeWindow2</ScreenID>
		<!--<Font/>-->
		<RelativePosition>false</RelativePosition>
		<Location>
			<X>0</X>
			<Y>0</Y>
		</Location>
		<Size>
			<CX>400</CX>
			<CY>25</CY>
		</Size>
		<Style_Transparent>false</Style_Transparent>
		<!--<TooltipReference/>-->
		<DrawTemplate>WDT_RoundedNoTitle</DrawTemplate>
		<Style_Titlebar>false</Style_Titlebar>
		<Style_Closebox>false</Style_Closebox>
		<Style_Minimizebox>false</Style_Minimizebox>
		<Style_Border>true</Style_Border>
		<Style_Sizable>false</Style_Sizable>
		<Pieces>EXW_CharInfoLabel</Pieces>
	</Screen>

	<Label item = "EXW_InfoLabel1">
		<ScreenID>EXW_InfoLabel1</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>5</X>
			<Y>2</Y>
		</Location>
		<Size>
			<CX>390</CX>
			<CY>15</CY>
		</Size>
		<Text>Click on a character to select that character.</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<AlignCenter>true</AlignCenter>
	</Label>

	<Label item = "EXW_InfoLabel2">
		<ScreenID>EXW_InfoLabel2</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>5</X>
			<Y>18</Y>
		</Location>
		<Size>
			<CX>390</CX>
			<CY>15</CY>
		</Size>
		<Text>Press Enter to enter the game, or press Esc to leave explore mode.</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<AlignCenter>true</AlignCenter>
	</Label>

	<Screen item = "EXW_ExploreModeWindow3">
		<ScreenID>EXW_ExploreModeWindow3</ScreenID>
		<!--<Font/>-->
		<RelativePosition>false</RelativePosition>
		<Location>
			<X>0</X>
			<Y>420</Y>
		</Location>
		<Size>
			<CX>400</CX>
			<CY>40</CY>
		</Size>
		<Style_Transparent>false</Style_Transparent>
		<!--<TooltipReference/>-->
		<DrawTemplate>WDT_RoundedNoTitle</DrawTemplate>
		<Style_Titlebar>false</Style_Titlebar>
		<Style_Closebox>false</Style_Closebox>
		<Style_Minimizebox>false</Style_Minimizebox>
		<Style_Border>true</Style_Border>
		<Style_Sizable>false</Style_Sizable>
		<Pieces>EXW_InfoLabel1</Pieces>
		<Pieces>EXW_InfoLabel2</Pieces>
	</Screen>


	<Screen item = "ExploreModeWindow">
		<!--<ScreenID/>-->
		<!--<Font/>-->
		<RelativePosition>false</RelativePosition>
		<Location>
			<X>0</X>
			<Y>0</Y>
		</Location>
		<Size>
			<CX>400</CX>
			<CY>20</CY>
		</Size>
		<Style_Transparent>true</Style_Transparent>
		<!--<TooltipReference/>-->
		<DrawTemplate>WDT_RoundedNoTitle</DrawTemplate>
		<Style_Titlebar>false</Style_Titlebar>
		<Style_Closebox>false</Style_Closebox>
		<Style_Minimizebox>false</Style_Minimizebox>
		<Style_Border>false</Style_Border>
		<Style_Sizable>false</Style_Sizable>
		<Pieces>EXW_ExploreModeWindow2</Pieces>
		<Pieces>EXW_ExploreModeWindow3</Pieces>
	</Screen>

</XML>
